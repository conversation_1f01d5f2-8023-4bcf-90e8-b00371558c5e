<?php
/**
 * Add Category Page - صفحة إضافة تصنيف
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Category.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize category class
$category_class = new Category($db);

// Get all categories for parent selection
$all_categories = $category_class->getAllCategories(false);

$errors = array();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
    $sort_order = (int)($_POST['sort_order'] ?? 0);
    
    // Validation
    if (empty($name)) {
        $errors[] = "اسم التصنيف مطلوب";
    }
    
    if (empty($errors)) {
        // Handle image upload
        $image_filename = '';
        if (!empty($_FILES['image']['tmp_name'])) {
            $upload_dir = UPLOAD_PATH . 'categories/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $image_filename = upload_image($_FILES['image'], $upload_dir);
            if (!$image_filename) {
                $errors[] = "حدث خطأ أثناء رفع الصورة";
            }
        }
        
        if (empty($errors)) {
            $category_data = array(
                'name' => $name,
                'description' => $description,
                'image' => $image_filename,
                'parent_id' => $parent_id,
                'sort_order' => $sort_order
            );
            
            if ($category_class->createCategory($category_data)) {
                header('Location: index.php?added=success');
                exit();
            } else {
                $errors[] = "حدث خطأ أثناء إضافة التصنيف";
            }
        }
    }
}

$page_title = "إضافة تصنيف جديد - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .image-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .image-upload-area:hover {
            border-color: #0d6efd;
            background: #f8f9ff;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="index.php" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i> </a>
                    <h4 class="mb-0">إضافة تصنيف جديد</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt ms-2 ms-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle ms-2 ms-1"></i> <strong>يرجى تصحيح الأخطاء التالية:</strong>
                <ul class="mb-0 mt-2">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" id="categoryForm">
            <div class="row">
                <!-- Main Information -->
                <div class="col-lg-8">
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle ms-2 ms-1"></i>معلومات التصنيف
                        </h5>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم التصنيف *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" 
                                   required autofocus>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف التصنيف</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            <small class="text-muted">وصف مختصر عن التصنيف وما يحتويه من منتجات</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">التصنيف الأب</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">تصنيف رئيسي</option>
                                    <?php foreach ($all_categories as $category): ?>
                                        <?php if (!$category['parent_id']): // Only show main categories ?>
                                            <option value="<?php echo $category['id']; ?>" 
                                                    <?php echo (isset($_POST['parent_id']) && $_POST['parent_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo $category['name']; ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                                <small class="text-muted">اتركه فارغاً إذا كان تصنيفاً رئيسياً</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="sort_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?php echo $_POST['sort_order'] ?? '0'; ?>" min="0">
                                <small class="text-muted">الرقم الأصغر يظهر أولاً</small>
                            </div>
                        </div>
                    </div>

                    <!-- Category Image -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-image ms-2 ms-1"></i>صورة التصنيف
                        </h5>
                        
                        <div class="image-upload-area" onclick="document.getElementById('image').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3 ms-1"></i> <h6>اضغط لرفع صورة أو اسحبها هنا</h6>
                            <p class="text-muted mb-0">JPG, PNG, GIF, WEBP (الحد الأقصى 5MB)</p>
                            <input type="file" id="image" name="image" accept="image/*" class="d-none">
                        </div>
                        
                        <div id="imagePreview"></div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Publish Options -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-cog ms-2 ms-1"></i>خيارات النشر
                        </h5>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save ms-2 ms-1"></i>حفظ التصنيف
                            </button>
                            
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times ms-2 ms-1"></i>إلغاء
                            </a>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="form-section">
                        <h6 class="mb-3">
                            <i class="fas fa-lightbulb ms-2 ms-1"></i>نصائح
                        </h6>
                        
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>اختر اسماً واضحاً ومفهوماً
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>استخدم صورة معبرة عن التصنيف
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>اكتب وصفاً مفيداً للعملاء
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>رتب التصنيفات حسب الأهمية
                            </li>
                        </ul>
                    </div>

                    <!-- Category Structure -->
                    <?php if (!empty($all_categories)): ?>
                        <div class="form-section">
                            <h6 class="mb-3">
                                <i class="fas fa-sitemap ms-2 ms-1"></i>هيكل التصنيفات الحالي
                            </h6>
                            
                            <div class="small">
                                <?php foreach ($all_categories as $category): ?>
                                    <?php if (!$category['parent_id']): ?>
                                        <div class="mb-2">
                                            <i class="fas fa-folder text-primary ms-2 ms-1"></i> <strong><?php echo $category['name']; ?></strong>
                                            
                                            <?php
                                            $subcategories = $category_class->getSubcategories($category['id']);
                                            foreach ($subcategories as $sub):
                                            ?>
                                                <div class="ms-4 mt-1">
                                                    <i class="fas fa-folder-open text-secondary ms-2 ms-1"></i> <?php echo $sub['name']; ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Image upload handling
        const imageInput = document.getElementById('image');
        const imagePreview = document.getElementById('imagePreview');
        const uploadArea = document.querySelector('.image-upload-area');
        
        imageInput.addEventListener('change', handleImageSelect);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#0d6efd';
            this.style.background = '#f8f9ff';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#dee2e6';
            this.style.background = '';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#dee2e6';
            this.style.background = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                imageInput.files = files;
                handleImageSelect();
            }
        });
        
        function handleImageSelect() {
            const file = imageInput.files[0];
            imagePreview.innerHTML = '';
            
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.innerHTML = `
                        <div class="text-center">
                            <img src="${e.target.result}" class="image-preview" alt="Preview">
                            <div class="mt-2">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeImage()">
                                    <i class="fas fa-trash ms-1 ms-1"></i>إزالة الصورة
                                </button>
                            </div>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        }
        
        function removeImage() {
            imageInput.value = '';
            imagePreview.innerHTML = '';
        }
        
        // Form validation
        document.getElementById('categoryForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            
            if (!name) {
                e.preventDefault();
                alert('اسم التصنيف مطلوب');
                document.getElementById('name').focus();
            }
        });
        
        // Auto-generate sort order
        document.addEventListener('DOMContentLoaded', function() {
            const sortOrderInput = document.getElementById('sort_order');
            if (!sortOrderInput.value) {
                // Set default sort order based on existing categories count
                const categoriesCount = <?php echo count($all_categories); ?>;
                sortOrderInput.value = categoriesCount + 1;
            }
        });
    </script>
</body>
</html>
