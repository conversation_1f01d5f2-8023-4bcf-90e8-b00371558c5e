<?php
/**
 * User Class
 * فئة المستخدمين
 */

class User {
    private $conn;
    private $table_name = "users";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Register new user
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        // Check if username or email already exists
        if ($this->usernameExists($data['username']) || $this->emailExists($data['email'])) {
            return false;
        }
        
        $query = "INSERT INTO " . $this->table_name . " 
                  (username, email, password, first_name, last_name, phone, address, city) 
                  VALUES (:username, :email, :password, :first_name, :last_name, :phone, :address, :city)";
        
        $stmt = $this->conn->prepare($query);
        
        // Hash password
        $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $stmt->bindParam(':username', $data['username']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':password', $hashed_password);
        $stmt->bindParam(':first_name', $data['first_name']);
        $stmt->bindParam(':last_name', $data['last_name']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':city', $data['city']);
        
        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }

    /**
     * Login user
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        $query = "SELECT id, username, email, password, first_name, last_name, role 
                  FROM " . $this->table_name . " 
                  WHERE (username = :username OR email = :username) AND is_active = 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            
            return $user;
        }
        
        return false;
    }

    /**
     * Logout user
     * تسجيل خروج المستخدم
     */
    public function logout() {
        session_unset();
        session_destroy();
        return true;
    }

    /**
     * Get user by ID
     * الحصول على مستخدم بالمعرف
     */
    public function getUserById($id) {
        $query = "SELECT id, username, email, first_name, last_name, phone, address, city, country, role, created_at 
                  FROM " . $this->table_name . " 
                  WHERE id = :id AND is_active = 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update user profile
     * تحديث ملف المستخدم
     */
    public function updateProfile($id, $data) {
        $query = "UPDATE " . $this->table_name . " 
                  SET first_name = :first_name, last_name = :last_name, phone = :phone, 
                      address = :address, city = :city, country = :country,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':first_name', $data['first_name']);
        $stmt->bindParam(':last_name', $data['last_name']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':city', $data['city']);
        $stmt->bindParam(':country', $data['country']);
        
        return $stmt->execute();
    }

    /**
     * Change password
     * تغيير كلمة المرور
     */
    public function changePassword($id, $current_password, $new_password) {
        // Verify current password
        $query = "SELECT password FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user || !password_verify($current_password, $user['password'])) {
            return false;
        }
        
        // Update password
        $update_query = "UPDATE " . $this->table_name . " 
                         SET password = :password, updated_at = CURRENT_TIMESTAMP 
                         WHERE id = :id";
        
        $update_stmt = $this->conn->prepare($update_query);
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        
        $update_stmt->bindParam(':id', $id);
        $update_stmt->bindParam(':password', $hashed_password);
        
        return $update_stmt->execute();
    }

    /**
     * Check if username exists
     * التحقق من وجود اسم المستخدم
     */
    public function usernameExists($username) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Check if email exists
     * التحقق من وجود البريد الإلكتروني
     */
    public function emailExists($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Get all users (admin only)
     * الحصول على جميع المستخدمين للإدارة
     */
    public function getAllUsers($page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT id, username, email, first_name, last_name, phone, role, is_active, created_at 
                  FROM " . $this->table_name . " 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update user status (admin only)
     * تحديث حالة المستخدم للإدارة
     */
    public function updateUserStatus($id, $is_active) {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_active = :is_active, updated_at = CURRENT_TIMESTAMP 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':is_active', $is_active);
        
        return $stmt->execute();
    }

    /**
     * Get user statistics
     * الحصول على إحصائيات المستخدمين
     */
    public function getUserStats() {
        $query = "SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_registrations
                  FROM " . $this->table_name;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get total users count
     * الحصول على إجمالي عدد المستخدمين
     */
    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Get new users today
     * الحصول على المستخدمين الجدد اليوم
     */
    public function getNewUsersToday() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . "
                  WHERE DATE(created_at) = CURDATE()";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }



    /**
     * Update user role
     * تحديث دور المستخدم
     */
    public function updateUserRole($user_id, $role) {
        $query = "UPDATE {$this->table_name} SET role = :role WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Get users with filters
     * الحصول على المستخدمين مع الفلاتر
     */
    public function getUsersWithFilters($page = 1, $limit = 20, $where_conditions = [], $params = []) {
        $offset = ($page - 1) * $limit;

        $query = "SELECT id, username, email, first_name, last_name, phone, role, is_active, created_at
                  FROM {$this->table_name}";

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }

        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total users with filters
     * الحصول على إجمالي المستخدمين مع الفلاتر
     */
    public function getTotalUsersWithFilters($where_conditions = [], $params = []) {
        $query = "SELECT COUNT(*) as total FROM {$this->table_name}";

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Get active users count
     * الحصول على عدد المستخدمين النشطين
     */
    public function getActiveUsersCount() {
        $query = "SELECT COUNT(*) as total FROM {$this->table_name} WHERE is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Get users by role
     * الحصول على المستخدمين حسب الدور
     */
    public function getUsersByRole($role) {
        $query = "SELECT COUNT(*) as total FROM {$this->table_name} WHERE role = :role";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Get new users in period
     * الحصول على المستخدمين الجدد في فترة
     */
    public function getNewUsersInPeriod($date_from, $date_to) {
        $query = "SELECT COUNT(*) as total FROM {$this->table_name}
                  WHERE DATE(created_at) BETWEEN :date_from AND :date_to";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':date_from', $date_from);
        $stmt->bindParam(':date_to', $date_to);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

}
