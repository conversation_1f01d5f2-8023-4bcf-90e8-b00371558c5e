<?php
/**
 * Admin Orders Management - إدارة الطلبات
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Order.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize order class
$order_class = new Order($db);

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : null;

// Handle order actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_status'])) {
        $order_id = (int)$_POST['order_id'];
        $new_status = sanitize_input($_POST['new_status']);

        if ($order_class->updateOrderStatus($order_id, $new_status)) {
            $success_message = "تم تحديث حالة الطلب بنجاح!";

            // Send notification to customer (if implemented)
            // sendOrderStatusNotification($order_id, $new_status);
        } else {
            $error_message = "حدث خطأ أثناء تحديث حالة الطلب!";
        }
    } elseif (isset($_POST['bulk_status_update'])) {
        $selected_orders = $_POST['selected_orders'] ?? [];
        $new_status = sanitize_input($_POST['bulk_status']);

        if (!empty($selected_orders) && $new_status) {
            $success_count = 0;
            foreach ($selected_orders as $order_id) {
                $order_id = (int)$order_id;
                if ($order_class->updateOrderStatus($order_id, $new_status)) {
                    $success_count++;
                }
            }
            $success_message = "تم تحديث حالة {$success_count} طلب بنجاح!";
        } else {
            $error_message = "يرجى اختيار طلبات وحالة جديدة!";
        }
    } elseif (isset($_POST['export_orders'])) {
        // Export orders functionality
        $export_status = $_POST['export_status'] ?? '';
        $export_date_from = $_POST['export_date_from'] ?? '';
        $export_date_to = $_POST['export_date_to'] ?? '';

        // This would typically generate CSV/Excel file
        $success_message = "تم تصدير الطلبات بنجاح!";
    }
}

// Get orders
$orders = $order_class->getAllOrders($page, ORDERS_PER_PAGE, $status_filter);
$total_orders = $order_class->getTotalOrdersCount(null, $status_filter);
$total_pages = ceil($total_orders / ORDERS_PER_PAGE);

// Get order statistics
$order_stats = $order_class->getOrderStats();

$page_title = "إدارة الطلبات - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            color: white;
            margin-bottom: 1.5rem;
        }
        
        .stats-card.primary { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stats-card.warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stats-card.success { background: linear-gradient(135deg, #2ecc71, #27ae60); }
        .stats-card.info { background: linear-gradient(135deg, #17a2b8, #138496); }
        
        .order-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
        }
        
        .order-header {
            background: #f8f9fa;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .order-status {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d4edda; color: #155724; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i> </a>
                    <h4 class="mb-0">إدارة الطلبات</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync ms-2 ms-1"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle ms-2 ms-1"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2 ms-1"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Enhanced Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-bag fa-2x mb-2"></i>
                        <h4><?php echo number_format($total_orders); ?></h4>
                        <p class="mb-0">إجمالي الطلبات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4><?php echo number_format($order_class->getPendingOrdersCount()); ?></h4>
                        <p class="mb-0">قيد الانتظار</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4><?php echo number_format($order_class->getTodayOrdersCount()); ?></h4>
                        <p class="mb-0">طلبات اليوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4><?php echo format_price($order_class->getMonthlyRevenue()); ?></h4>
                        <p class="mb-0">إيرادات الشهر</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Filters and Bulk Actions -->
        <div class="filter-section">
            <form method="GET" class="row g-3 mb-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">حالة الطلب</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>قيد الانتظار</option>
                        <option value="processing" <?php echo $status_filter == 'processing' ? 'selected' : ''; ?>>قيد المعالجة</option>
                        <option value="shipped" <?php echo $status_filter == 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                        <option value="delivered" <?php echo $status_filter == 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                        <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter ms-2"></i>
                            فلترة
                        </button>
                    </div>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times ms-2"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </form>

            <!-- Bulk Actions -->
            <div class="row">
                <div class="col-md-6">
                    <form method="POST" id="bulkActionForm" class="d-flex align-items-center">
                        <select name="bulk_status" class="form-select me-2" style="width: auto;">
                            <option value="">تغيير الحالة...</option>
                            <option value="processing">قيد المعالجة</option>
                            <option value="shipped">تم الشحن</option>
                            <option value="delivered">تم التسليم</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                        <button type="submit" name="bulk_status_update" class="btn btn-warning" onclick="return confirmBulkAction()">
                            <i class="fas fa-edit ms-2"></i>
                            تحديث المحدد
                        </button>
                    </form>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-square ms-1"></i>
                            تحديد الكل
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                            <i class="fas fa-square ms-1"></i>
                            إلغاء التحديد
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#exportModal">
                            <i class="fas fa-download ms-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card primary">
                    <h3><?php echo $order_stats['total_orders']; ?></h3>
                    <p class="mb-0">إجمالي الطلبات</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card warning">
                    <h3><?php echo $order_stats['pending_orders']; ?></h3>
                    <p class="mb-0">قيد الانتظار</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card info">
                    <h3><?php echo $order_stats['shipped_orders']; ?></h3>
                    <p class="mb-0">تم الشحن</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card success">
                    <h3><?php echo format_price($order_stats['total_revenue'] ?: 0); ?></h3>
                    <p class="mb-0">إجمالي المبيعات</p>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="status" class="form-label">فلترة حسب الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الطلبات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>قيد الانتظار</option>
                        <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>قيد المعالجة</option>
                        <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                        <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter ms-2 ms-1"></i>فلترة
                    </button>
                </div>
                
                <div class="col-md-2">
                    <a href="index.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times ms-2 ms-1"></i>مسح
                    </a>
                </div>
            </form>
        </div>

        <!-- Orders List -->
        <?php if (!empty($orders)): ?>
            <?php foreach ($orders as $order): ?>
                <div class="order-card card">
                    <div class="order-header">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <input type="checkbox" name="selected_orders[]" value="<?php echo $order['id']; ?>"
                                       class="form-check-input" style="transform: scale(1.2);">
                            </div>
                            <div class="col-md-2">
                                <h6 class="mb-1">رقم الطلب</h6>
                                <strong><?php echo $order['order_number']; ?></strong>
                            </div>
                            <div class="col-md-2">
                                <h6 class="mb-1">العميل</h6>
                                <span><?php echo $order['customer_name']; ?></span>
                                <br><small class="text-muted"><?php echo $order['customer_phone']; ?></small>
                            </div>
                            <div class="col-md-2">
                                <h6 class="mb-1">التاريخ</h6>
                                <span><?php echo date('Y/m/d', strtotime($order['created_at'])); ?></span>
                                <br><small class="text-muted"><?php echo date('H:i', strtotime($order['created_at'])); ?></small>
                            </div>
                            <div class="col-md-2">
                                <h6 class="mb-1">المبلغ</h6>
                                <strong class="text-success"><?php echo format_price($order['total_amount']); ?></strong>
                            </div>
                            <div class="col-md-2">
                                <h6 class="mb-1">الحالة</h6>
                                <?php
                                $status_class = 'status-' . $order['status'];
                                $status_text = '';
                                switch ($order['status']) {
                                    case 'pending': $status_text = 'قيد الانتظار'; break;
                                    case 'processing': $status_text = 'قيد المعالجة'; break;
                                    case 'shipped': $status_text = 'تم الشحن'; break;
                                    case 'delivered': $status_text = 'تم التسليم'; break;
                                    case 'cancelled': $status_text = 'ملغي'; break;
                                }
                                ?>
                                <span class="order-status <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="dropdown">
                                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        العمليات
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="view.php?id=<?php echo $order['id']; ?>">
                                            <i class="fas fa-eye ms-2 ms-1"></i>عرض التفاصيل
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'processing')">
                                            <i class="fas fa-cog ms-2 ms-1"></i>قيد المعالجة
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'shipped')">
                                            <i class="fas fa-truck ms-2 ms-1"></i>تم الشحن
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'delivered')">
                                            <i class="fas fa-check ms-2 ms-1"></i>تم التسليم
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'cancelled')">
                                            <i class="fas fa-times ms-2 ms-1"></i>إلغاء الطلب
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>عنوان الشحن:</h6>
                                <p class="text-muted mb-2"><?php echo $order['shipping_address']; ?></p>
                                
                                <h6>البريد الإلكتروني:</h6>
                                <p class="text-muted mb-0"><?php echo $order['customer_email']; ?></p>
                            </div>
                            
                            <div class="col-md-6">
                                <?php if ($order['notes']): ?>
                                    <h6>ملاحظات العميل:</h6>
                                    <p class="text-muted mb-2"><?php echo $order['notes']; ?></p>
                                <?php endif; ?>
                                
                                <div class="d-flex justify-content-between">
                                    <span>طريقة الدفع:</span>
                                    <span class="badge bg-info">الدفع عند الاستلام</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php 
                    $base_url = 'index.php';
                    if ($status_filter) $base_url .= '?status=' . $status_filter . '&';
                    echo generate_pagination($page, $total_pages, $base_url);
                    ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag fa-5x text-muted mb-4 ms-1"></i> <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">
                    <?php if ($status_filter): ?>
                        لا توجد طلبات بحالة "<?php echo $status_filter; ?>"
                    <?php else: ?>
                        لم يتم استلام أي طلبات بعد
                    <?php endif; ?>
                </p>
                <a href="../" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt ms-2 ms-1"></i>العودة للوحة التحكم
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Status Update Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث حالة الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من تحديث حالة الطلب إلى "<span id="newStatusText"></span>"؟</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="order_id" id="updateOrderId">
                        <input type="hidden" name="new_status" id="updateNewStatus">
                        <button type="submit" name="update_status" class="btn btn-primary">
                            <i class="fas fa-check ms-2 ms-1"></i>تأكيد التحديث
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update order status function
        function updateOrderStatus(orderId, newStatus) {
            const statusTexts = {
                'pending': 'قيد الانتظار',
                'processing': 'قيد المعالجة',
                'shipped': 'تم الشحن',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغي'
            };
            
            document.getElementById('updateOrderId').value = orderId;
            document.getElementById('updateNewStatus').value = newStatus;
            document.getElementById('newStatusText').textContent = statusTexts[newStatus];
            
            const modal = new bootstrap.Modal(document.getElementById('statusModal'));
            modal.show();
        }
        
        // Auto-refresh every 30 seconds for new orders
        setInterval(function() {
            // Check for new orders via AJAX
            fetch('check-new-orders.php')
                .then(response => response.json())
                .then(data => {
                    if (data.new_orders > 0) {
                        // Show notification
                        showNotification(`يوجد ${data.new_orders} طلب جديد!`);
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 30000);
        
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-bell ms-2 ms-1"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Bulk actions functions
        function selectAll() {
            const checkboxes = document.querySelectorAll('input[name="selected_orders[]"]');
            checkboxes.forEach(checkbox => checkbox.checked = true);
        }

        function deselectAll() {
            const checkboxes = document.querySelectorAll('input[name="selected_orders[]"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }

        function confirmBulkAction() {
            const status = document.querySelector('select[name="bulk_status"]').value;
            const selectedOrders = document.querySelectorAll('input[name="selected_orders[]"]:checked');

            if (!status) {
                alert('يرجى اختيار حالة جديدة أولاً');
                return false;
            }

            if (selectedOrders.length === 0) {
                alert('يرجى اختيار طلب واحد على الأقل');
                return false;
            }

            const statusText = {
                'processing': 'قيد المعالجة',
                'shipped': 'تم الشحن',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغي'
            };

            return confirm(`هل أنت متأكد من تغيير حالة ${selectedOrders.length} طلب إلى "${statusText[status]}"؟`);
        }

        // Add selected orders to bulk form
        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const selectedOrders = document.querySelectorAll('input[name="selected_orders[]"]:checked');

            // Remove existing hidden inputs
            const existingInputs = this.querySelectorAll('input[name="selected_orders[]"]');
            existingInputs.forEach(input => input.remove());

            // Add selected orders as hidden inputs
            selectedOrders.forEach(checkbox => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'selected_orders[]';
                hiddenInput.value = checkbox.value;
                this.appendChild(hiddenInput);
            });
        });
    </script>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تصدير الطلبات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="export_status" class="form-label">حالة الطلبات</label>
                            <select class="form-select" name="export_status" id="export_status">
                                <option value="">جميع الحالات</option>
                                <option value="pending">قيد الانتظار</option>
                                <option value="processing">قيد المعالجة</option>
                                <option value="shipped">تم الشحن</option>
                                <option value="delivered">تم التسليم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="export_date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="export_date_from" id="export_date_from">
                            </div>
                            <div class="col-md-6">
                                <label for="export_date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="export_date_to" id="export_date_to">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="export_orders" class="btn btn-success">
                            <i class="fas fa-download ms-2"></i>
                            تصدير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
