/* Enhanced Wishlist Page Styles */

/* Wishlist Header */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Enhanced Empty State */
.wishlist-empty {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    margin: 2rem 0;
    border: 2px dashed #dee2e6;
}

.wishlist-empty .empty-icon {
    margin-bottom: 2rem;
}

.wishlist-empty .empty-icon i {
    color: #dee2e6;
    animation: float 3s ease-in-out infinite;
}

.wishlist-empty h3 {
    color: #6c757d;
    margin-bottom: 1rem;
    font-weight: 600;
}

.wishlist-empty p {
    color: #adb5bd;
    margin-bottom: 2rem;
    line-height: 1.6;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Remove <PERSON> */
.wishlist-remove-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 42px;
    height: 42px;
    background: rgba(231, 76, 60, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 25;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    backdrop-filter: blur(15px);
    cursor: pointer;
}

.wishlist-remove-btn:hover {
    background: rgba(192, 57, 43, 1);
    transform: scale(1.15) rotate(10deg);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
    border-color: rgba(255, 255, 255, 0.5);
}

.wishlist-remove-btn:active {
    transform: scale(1.05) rotate(5deg);
}

/* Wishlist Item Animations */
.wishlist-item {
    transition: all 0.4s ease;
}

.wishlist-item.removing {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
    pointer-events: none;
}

/* Enhanced Wishlist Stats */
.wishlist-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.wishlist-stats .stat-item {
    text-align: center;
    color: white;
}

.wishlist-stats .stat-number {
    font-size: 2rem;
    font-weight: 700;
    display: block;
}

.wishlist-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Continue Shopping Button Enhancement */
.continue-shopping {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 15px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.continue-shopping:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Wishlist Actions */
.wishlist-actions {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.bulk-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.bulk-actions .btn {
    border-radius: 20px;
    padding: 10px 20px;
    font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .wishlist-remove-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
        top: 8px;
        right: 8px;
    }
    
    .wishlist-empty {
        padding: 2rem 1rem;
    }
    
    .wishlist-empty .empty-icon i {
        font-size: 3rem;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .bulk-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

/* Smooth Transitions */
.wishlist-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.wishlist-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wishlist-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}
