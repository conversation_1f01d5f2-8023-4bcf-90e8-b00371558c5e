<?php
/**
 * Get Attribute Values - AJAX endpoint
 * الحصول على قيم الخصائص
 */

require_once '../../config/config.php';
require_once '../../classes/ProductVariant.php';

// Check if user is admin
require_admin();

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid attribute ID']);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $variant_class = new ProductVariant($db);
    
    $attribute_id = (int)$_GET['id'];
    $values = $variant_class->getAttributeValues($attribute_id);
    
    echo json_encode($values);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
