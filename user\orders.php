<?php
/**
 * User Orders Page - صفحة طلبات العميل
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/Order.php';

// Require login
require_login();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$order_class = new Order($db);

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : null;

// Get user orders
$orders = $order_class->getUserOrders($_SESSION['user_id'], $page, ORDERS_PER_PAGE);
$total_orders = $order_class->getTotalOrdersCount($_SESSION['user_id'], $status_filter);
$total_pages = ceil($total_orders / ORDERS_PER_PAGE);

$page_title = "طلباتي - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .order-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
        }
        
        .order-header {
            background: #f8f9fa;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .order-status {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d4edda; color: #155724; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .sidebar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover {
            background: #667eea;
            color: white;
        }
        
        .sidebar .nav-link.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-shopping-bag me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="dashboard.php">لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-user-circle me-2"></i>
                        حسابي
                    </h5>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                        <a class="nav-link active" href="orders.php">
                            <i class="fas fa-shopping-bag me-2"></i>
                            طلباتي
                        </a>
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>
                            الملف الشخصي
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-shopping-bag me-2"></i>
                        طلباتي
                    </h2>
                    
                    <!-- Status Filter -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>
                            فلترة حسب الحالة
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="orders.php">جميع الطلبات</a></li>
                            <li><a class="dropdown-item" href="orders.php?status=pending">قيد الانتظار</a></li>
                            <li><a class="dropdown-item" href="orders.php?status=processing">قيد المعالجة</a></li>
                            <li><a class="dropdown-item" href="orders.php?status=shipped">تم الشحن</a></li>
                            <li><a class="dropdown-item" href="orders.php?status=delivered">تم التسليم</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Orders List -->
                <?php if (!empty($orders)): ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="order-card card">
                            <div class="order-header">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">رقم الطلب</h6>
                                        <strong><?php echo $order['order_number']; ?></strong>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">تاريخ الطلب</h6>
                                        <span><?php echo date('Y/m/d H:i', strtotime($order['created_at'])); ?></span>
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">الحالة</h6>
                                        <?php
                                        $status_class = 'status-' . $order['status'];
                                        $status_text = '';
                                        switch ($order['status']) {
                                            case 'pending': $status_text = 'قيد الانتظار'; break;
                                            case 'processing': $status_text = 'قيد المعالجة'; break;
                                            case 'shipped': $status_text = 'تم الشحن'; break;
                                            case 'delivered': $status_text = 'تم التسليم'; break;
                                            case 'cancelled': $status_text = 'ملغي'; break;
                                        }
                                        ?>
                                        <span class="order-status <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <h6 class="mb-1">المبلغ الإجمالي</h6>
                                        <strong class="h5 text-primary"><?php echo format_price($order['total_amount']); ?></strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6>عنوان الشحن:</h6>
                                        <p class="text-muted mb-2"><?php echo $order['shipping_address']; ?></p>
                                        
                                        <?php if ($order['notes']): ?>
                                            <h6>ملاحظات:</h6>
                                            <p class="text-muted mb-0"><?php echo $order['notes']; ?></p>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="col-md-4 text-end">
                                        <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل
                                        </a>
                                        
                                        <?php if ($order['status'] === 'pending'): ?>
                                            <button class="btn btn-outline-danger ms-2" 
                                                    onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-times me-2"></i>
                                                إلغاء الطلب
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <?php 
                            $base_url = 'orders.php';
                            if ($status_filter) $base_url .= '?status=' . $status_filter . '&';
                            echo generate_pagination($page, $total_pages, $base_url);
                            ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-4x text-muted mb-4"></i>
                        <h4>لا توجد طلبات</h4>
                        <p class="text-muted mb-4">
                            <?php if ($status_filter): ?>
                                لا توجد طلبات بحالة "<?php echo $status_filter; ?>"
                            <?php else: ?>
                                لم تقم بإجراء أي طلبات حتى الآن
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo BASE_URL; ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-shopping-cart me-2"></i>
                            ابدأ التسوق الآن
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function cancelOrder(orderId) {
            if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                // Send AJAX request to cancel order
                fetch('../api/cancel-order.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        order_id: orderId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إلغاء الطلب بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء إلغاء الطلب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء إلغاء الطلب');
                });
            }
        }
    </script>
</body>
</html>
