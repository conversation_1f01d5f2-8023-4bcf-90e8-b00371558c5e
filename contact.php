<?php
/**
 * Contact Us Page - صفحة اتصل بنا
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/image_helper.php';

$page_title = 'اتصل بنا - ' . SITE_NAME;

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    $errors = [];
    
    if (empty($name)) $errors[] = 'الاسم مطلوب';
    if (empty($email)) $errors[] = 'البريد الإلكتروني مطلوب';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'البريد الإلكتروني غير صحيح';
    if (empty($subject)) $errors[] = 'الموضوع مطلوب';
    if (empty($message)) $errors[] = 'الرسالة مطلوبة';
    
    if (empty($errors)) {
        // Here you would typically send email or save to database
        $success_message = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    
    <style>
        .contact-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .contact-card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
        }
        
        .contact-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
        
        .map-container {
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <!-- Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">اتصل بنا</h1>
                <p class="lead">نحن هنا لمساعدتك! تواصل معنا في أي وقت</p>
            </div>
        </div>
    </section>
    
    <!-- Contact Information -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="contact-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h5>العنوان</h5>
                            <p class="text-muted mb-0">
                                الرياض، المملكة العربية السعودية<br>
                                شارع الملك فهد، حي العليا<br>
                                مجمع الأعمال التجاري
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="contact-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <h5>الهاتف</h5>
                            <p class="text-muted mb-0">
                                <a href="tel:+966501234567" class="text-decoration-none">
                                    +966 50 123 4567
                                </a><br>
                                <a href="tel:+966112345678" class="text-decoration-none">
                                    +966 11 234 5678
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="contact-card card h-100">
                        <div class="card-body text-center p-4">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h5>البريد الإلكتروني</h5>
                            <p class="text-muted mb-0">
                                <a href="mailto:<EMAIL>" class="text-decoration-none">
                                    <EMAIL>
                                </a><br>
                                <a href="mailto:<EMAIL>" class="text-decoration-none">
                                    <EMAIL>
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Contact Form -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg">
                        <div class="card-header bg-gradient-primary text-white text-center">
                            <h3 class="mb-0">
                                <i class="fas fa-paper-plane ms-2"></i>
                                أرسل لنا رسالة
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <?php if (isset($success_message)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle ms-2"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($errors)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle ms-2"></i>
                                    <ul class="mb-0">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?php echo $error; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">الموضوع *</label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">اختر الموضوع</option>
                                        <option value="general" <?php echo ($subject ?? '') == 'general' ? 'selected' : ''; ?>>استفسار عام</option>
                                        <option value="order" <?php echo ($subject ?? '') == 'order' ? 'selected' : ''; ?>>استفسار عن طلب</option>
                                        <option value="product" <?php echo ($subject ?? '') == 'product' ? 'selected' : ''; ?>>استفسار عن منتج</option>
                                        <option value="complaint" <?php echo ($subject ?? '') == 'complaint' ? 'selected' : ''; ?>>شكوى</option>
                                        <option value="suggestion" <?php echo ($subject ?? '') == 'suggestion' ? 'selected' : ''; ?>>اقتراح</option>
                                    </select>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="message" class="form-label">الرسالة *</label>
                                    <textarea class="form-control" id="message" name="message" rows="5" 
                                              placeholder="اكتب رسالتك هنا..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="fas fa-paper-plane ms-2"></i>
                                        إرسال الرسالة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <?php include 'includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>js/script.js"></script>
</body>
</html>
