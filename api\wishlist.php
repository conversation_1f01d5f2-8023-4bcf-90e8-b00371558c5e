<?php
/**
 * Wishlist API - واجهة برمجة تطبيقات المفضلة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/Product.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize product class
$product_class = new Product($db);

$response = array('success' => false, 'message' => '');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add':
                $product_id = (int)($_POST['product_id'] ?? 0);
                
                if ($product_id > 0) {
                    // Verify product exists and is active
                    $product = $product_class->getProductById($product_id);
                    
                    if ($product && $product['is_active']) {
                        // Initialize wishlist if not exists
                        if (!isset($_SESSION['wishlist'])) {
                            $_SESSION['wishlist'] = array();
                        }
                        
                        // Add to wishlist if not already there
                        if (!in_array($product_id, $_SESSION['wishlist'])) {
                            $_SESSION['wishlist'][] = $product_id;
                            $response['success'] = true;
                            $response['message'] = 'تم إضافة المنتج إلى المفضلة';
                            $response['wishlist_count'] = count($_SESSION['wishlist']);
                        } else {
                            $response['message'] = 'المنتج موجود بالفعل في المفضلة';
                        }
                    } else {
                        $response['message'] = 'المنتج غير موجود أو غير نشط';
                    }
                } else {
                    $response['message'] = 'معرف المنتج غير صحيح';
                }
                break;
                
            case 'remove':
                $product_id = (int)($_POST['product_id'] ?? 0);
                
                if ($product_id > 0 && isset($_SESSION['wishlist'])) {
                    $key = array_search($product_id, $_SESSION['wishlist']);
                    if ($key !== false) {
                        unset($_SESSION['wishlist'][$key]);
                        $_SESSION['wishlist'] = array_values($_SESSION['wishlist']); // Re-index array
                        $response['success'] = true;
                        $response['message'] = 'تم إزالة المنتج من المفضلة';
                        $response['wishlist_count'] = count($_SESSION['wishlist']);
                    } else {
                        $response['message'] = 'المنتج غير موجود في المفضلة';
                    }
                } else {
                    $response['message'] = 'معرف المنتج غير صحيح';
                }
                break;
                
            case 'clear':
                $_SESSION['wishlist'] = array();
                $response['success'] = true;
                $response['message'] = 'تم مسح المفضلة';
                $response['wishlist_count'] = 0;
                break;
                
            default:
                $response['message'] = 'إجراء غير صحيح';
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_count':
                $count = isset($_SESSION['wishlist']) ? count($_SESSION['wishlist']) : 0;
                $response['success'] = true;
                $response['count'] = $count;
                break;
                
            case 'get_items':
                $wishlist_items = array();
                
                if (isset($_SESSION['wishlist']) && !empty($_SESSION['wishlist'])) {
                    foreach ($_SESSION['wishlist'] as $product_id) {
                        $product = $product_class->getProductById($product_id);
                        if ($product && $product['is_active']) {
                            $wishlist_items[] = array(
                                'id' => $product['id'],
                                'name' => $product['name'],
                                'price' => $product['price'],
                                'sale_price' => $product['sale_price'],
                                'image' => $product['primary_image'],
                                'slug' => $product['slug']
                            );
                        }
                    }
                }
                
                $response['success'] = true;
                $response['items'] = $wishlist_items;
                $response['count'] = count($wishlist_items);
                break;
                
            case 'check_item':
                $product_id = (int)($_GET['product_id'] ?? 0);
                $is_in_wishlist = isset($_SESSION['wishlist']) && in_array($product_id, $_SESSION['wishlist']);
                
                $response['success'] = true;
                $response['in_wishlist'] = $is_in_wishlist;
                break;
                
            default:
                $response['message'] = 'إجراء غير صحيح';
        }
    } else {
        $response['message'] = 'طريقة الطلب غير مدعومة';
    }
    
} catch (Exception $e) {
    error_log('Wishlist API Error: ' . $e->getMessage());
    $response['message'] = 'حدث خطأ في الخادم';
}

// Return JSON response
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
