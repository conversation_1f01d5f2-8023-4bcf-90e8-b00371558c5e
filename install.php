<?php
/**
 * Installation Script - سكريبت التثبيت
 * Run this file once to set up the database and initial configuration
 */

// Check if already installed
if (file_exists('config/config.php')) {
    die('التطبيق مثبت بالفعل. احذف ملف config/config.php لإعادة التثبيت.');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$errors = array();
$success = false;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        // Database configuration step
        $db_host = $_POST['db_host'] ?? 'localhost';
        $db_name = $_POST['db_name'] ?? 'shoppy_db';
        $db_user = $_POST['db_user'] ?? '';
        $db_pass = $_POST['db_pass'] ?? '';
        
        // Test database connection
        try {
            $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if not exists
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$db_name`");
            
            // Store database config in session
            session_start();
            $_SESSION['install_db'] = array(
                'host' => $db_host,
                'name' => $db_name,
                'user' => $db_user,
                'pass' => $db_pass
            );
            
            header('Location: install.php?step=2');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
        }
    }
    
    if ($step === 2) {
        // Site configuration step
        session_start();
        
        if (!isset($_SESSION['install_db'])) {
            header('Location: install.php?step=1');
            exit();
        }
        
        $site_name = $_POST['site_name'] ?? 'متجر شوبي';
        $site_url = $_POST['site_url'] ?? 'http://localhost/shoppy/';
        $admin_username = $_POST['admin_username'] ?? 'admin';
        $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
        $admin_password = $_POST['admin_password'] ?? '';
        $admin_name = $_POST['admin_name'] ?? 'مدير النظام';
        
        if (empty($admin_password)) {
            $errors[] = "كلمة مرور المدير مطلوبة";
        }
        
        if (empty($errors)) {
            try {
                $db_config = $_SESSION['install_db'];
                
                // Connect to database
                $pdo = new PDO(
                    "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4",
                    $db_config['user'],
                    $db_config['pass']
                );
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Import database schema
                $sql_file = file_get_contents('database/shoppy_db.sql');
                $statements = explode(';', $sql_file);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement) && !preg_match('/^(--|CREATE DATABASE|USE)/i', $statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // Create admin user
                $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, role, is_active) 
                    VALUES (?, ?, ?, ?, '', 'admin', 1)
                    ON DUPLICATE KEY UPDATE 
                    password = VALUES(password), first_name = VALUES(first_name)
                ");
                $stmt->execute([$admin_username, $admin_email, $hashed_password, $admin_name]);
                
                // Create config file
                $config_content = file_get_contents('config/config.example.php');
                $config_content = str_replace([
                    "'localhost'",
                    "'shoppy_db'",
                    "'your_username'",
                    "'your_password'",
                    "'متجر شوبي'",
                    "'http://localhost/shoppy/'"
                ], [
                    "'{$db_config['host']}'",
                    "'{$db_config['name']}'",
                    "'{$db_config['user']}'",
                    "'{$db_config['pass']}'",
                    "'$site_name'",
                    "'$site_url'"
                ], $config_content);
                
                file_put_contents('config/config.php', $config_content);
                
                // Create upload directories
                $upload_dirs = [
                    'assets/images/products',
                    'assets/images/categories',
                    'logs',
                    'backups'
                ];
                
                foreach ($upload_dirs as $dir) {
                    if (!file_exists($dir)) {
                        mkdir($dir, 0755, true);
                    }
                }
                
                $success = true;
                
            } catch (Exception $e) {
                $errors[] = "خطأ في التثبيت: " . $e->getMessage();
            }
        }
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت متجر شوبي</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .install-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .success-animation {
            text-align: center;
            padding: 2rem 0;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <!-- Header -->
            <div class="install-header">
                <h2 class="mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>
                    تثبيت متجر شوبي
                </h2>
                <p class="mb-0 mt-2">مرحباً بك في معالج التثبيت</p>
            </div>
            
            <!-- Body -->
            <div class="install-body">
                <?php if ($success): ?>
                    <!-- Success Message -->
                    <div class="success-animation">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="text-success mb-3">تم التثبيت بنجاح! 🎉</h3>
                        <p class="mb-4">
                            تم إعداد متجر شوبي بنجاح. يمكنك الآن البدء في استخدام المتجر.
                        </p>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                            <ul class="mb-0">
                                <li><strong>اسم المستخدم:</strong> <?php echo $admin_username; ?></li>
                                <li><strong>البريد الإلكتروني:</strong> <?php echo $admin_email; ?></li>
                                <li><strong>كلمة المرور:</strong> التي أدخلتها</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="<?php echo $site_url; ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>
                                زيارة المتجر
                            </a>
                            <a href="<?php echo $site_url; ?>admin/" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-2"></i>
                                لوحة تحكم الإدارة
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                لأسباب أمنية، يرجى حذف ملف install.php بعد التثبيت
                            </small>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Step Indicator -->
                    <div class="step-indicator">
                        <div class="step <?php echo $step >= 1 ? 'active' : ''; ?>">1</div>
                        <div class="step <?php echo $step >= 2 ? 'active' : ''; ?>">2</div>
                    </div>
                    
                    <!-- Error Messages -->
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php foreach ($errors as $error): ?>
                                <div><?php echo $error; ?></div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step === 1): ?>
                        <!-- Step 1: Database Configuration -->
                        <h4 class="mb-4">الخطوة 1: إعداد قاعدة البيانات</h4>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" 
                                       value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" 
                                       value="<?php echo $_POST['db_name'] ?? 'shoppy_db'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_user" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" 
                                       value="<?php echo $_POST['db_user'] ?? ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass" 
                                       value="<?php echo $_POST['db_pass'] ?? ''; ?>">
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                سيتم إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة.
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-database me-2"></i>
                                    اختبار الاتصال والمتابعة
                                </button>
                            </div>
                        </form>
                        
                    <?php elseif ($step === 2): ?>
                        <!-- Step 2: Site Configuration -->
                        <h4 class="mb-4">الخطوة 2: إعداد الموقع</h4>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="site_name" class="form-label">اسم المتجر</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                       value="<?php echo $_POST['site_name'] ?? 'متجر شوبي'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_url" class="form-label">رابط الموقع</label>
                                <input type="url" class="form-control" id="site_url" name="site_url" 
                                       value="<?php echo $_POST['site_url'] ?? 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/'; ?>" required>
                            </div>
                            
                            <hr class="my-4">
                            
                            <h5 class="mb-3">حساب المدير</h5>
                            
                            <div class="mb-3">
                                <label for="admin_username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username" 
                                       value="<?php echo $_POST['admin_username'] ?? 'admin'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                       value="<?php echo $_POST['admin_email'] ?? '<EMAIL>'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="admin_name" name="admin_name" 
                                       value="<?php echo $_POST['admin_name'] ?? 'مدير النظام'; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" 
                                       required minlength="6">
                                <small class="text-muted">6 أحرف على الأقل</small>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-2"></i>
                                    إكمال التثبيت
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Requirements Check -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    فحص متطلبات النظام
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="d-flex justify-content-between">
                            <span>PHP Version:</span>
                            <span class="badge bg-<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'success' : 'danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex justify-content-between">
                            <span>PDO MySQL:</span>
                            <span class="badge bg-<?php echo extension_loaded('pdo_mysql') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex justify-content-between">
                            <span>GD Extension:</span>
                            <span class="badge bg-<?php echo extension_loaded('gd') ? 'success' : 'warning'; ?>">
                                <?php echo extension_loaded('gd') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex justify-content-between">
                            <span>File Uploads:</span>
                            <span class="badge bg-<?php echo ini_get('file_uploads') ? 'success' : 'danger'; ?>">
                                <?php echo ini_get('file_uploads') ? 'مفعل' : 'معطل'; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-detect site URL
        document.addEventListener('DOMContentLoaded', function() {
            const siteUrlInput = document.getElementById('site_url');
            if (siteUrlInput && !siteUrlInput.value.includes('localhost')) {
                const protocol = window.location.protocol;
                const host = window.location.host;
                const path = window.location.pathname.replace('/install.php', '/');
                siteUrlInput.value = protocol + '//' + host + path;
            }
        });
    </script>
</body>
</html>
