# إصلاح قاعدة البيانات - Database Fix

## المشكلة
الجدول `product_variant_attributes` مفقود من قاعدة البيانات مما يسبب خطأ عند محاولة الوصول لمتغيرات المنتجات.

## الحل

### الطريقة الأولى: استخدام phpMyAdmin
1. افتح phpMyAdmin من خلال `http://localhost/phpmyadmin`
2. اختر قاعدة البيانات `shoppy_ecommerce`
3. اذهب إلى تبويب "SQL"
4. انسخ والصق محتوى ملف `fix_missing_tables.sql`
5. اضغط "تنفيذ"

### الطريقة الثانية: استخدام سطر الأوامر
إذا كان MySQL متاح في PATH:
```bash
mysql -u root -p shoppy_ecommerce < database/fix_missing_tables.sql
```

### الطريقة الثالثة: استخدام XAMPP
1. افتح XAMPP Control Panel
2. اضغط "Shell"
3. نفذ الأمر:
```bash
cd /c/xampp/htdocs/shoppy
mysql -u root shoppy_ecommerce < database/fix_missing_tables.sql
```

## التحقق من الإصلاح
بعد تنفيذ الإصلاح، تأكد من:
1. عدم ظهور رسائل خطأ عند تصفح المنتجات
2. إمكانية تعديل المنتجات من لوحة التحكم
3. عمل نظام الصور بشكل صحيح

## ملاحظة
تم تعطيل متغيرات المنتجات مؤقتاً في `product.php` حتى يتم إنشاء الجداول المطلوبة.
