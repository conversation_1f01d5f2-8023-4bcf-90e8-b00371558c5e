/* Enhanced Product Page Styles - أنماط صفحة المنتج المحسنة */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* Product Container */
.product-container {
    padding: 3rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Enhanced Breadcrumb */
.breadcrumb {
    background: white;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.breadcrumb-item {
    font-weight: 500;
    color: #6c757d;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--primary-color);
    font-weight: bold;
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

/* Product Gallery Enhanced */
.product-gallery {
    position: relative;
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.main-image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: #f8f9fa;
}

.product-image-main {
    width: 100%;
    height: 500px;
    object-fit: cover;
    cursor: zoom-in;
    transition: var(--transition);
}

.product-image-main:hover {
    transform: scale(1.05);
}

.image-badges {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.sale-badge {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e74c3c 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.new-badge {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.zoom-icon {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--dark-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.zoom-icon:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Thumbnails */
.product-thumbnails {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    overflow-x: auto;
    padding: 10px 0;
}

.product-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    cursor: pointer;
    border: 3px solid transparent;
    border-radius: 10px;
    transition: var(--transition);
    flex-shrink: 0;
}

.product-thumbnail.active,
.product-thumbnail:hover {
    border-color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Product Info */
.product-info {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    height: fit-content;
}

.product-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stars {
    color: #ffc107;
    font-size: 1.2rem;
}

.rating-text {
    color: #6c757d;
    font-weight: 500;
}

.rating-count {
    color: var(--primary-color);
    font-weight: 600;
}

/* Price Section Enhanced */
.price-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin: 1.5rem 0;
    border: 2px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.price-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.price-current {
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 15px;
}

.price-original {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 1.4rem;
    font-weight: 500;
}

.discount-percentage {
    background: var(--danger-color);
    color: white;
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Stock Status */
.stock-status {
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: inline-block;
    font-size: 1rem;
}

.stock-in {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #b8dacc;
}

.stock-low {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 2px solid #f1c40f;
}

.stock-out {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #e74c3c;
}

/* Product Features */
.product-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.feature-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-text {
    font-weight: 500;
    color: var(--dark-color);
}

/* Quantity Selector Enhanced */
.quantity-selector {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.quantity-label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.quantity-btn {
    width: 45px;
    height: 45px;
    border: 2px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
    font-weight: bold;
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.quantity-input {
    width: 80px;
    text-align: center;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    background: white;
}

/* Action Buttons Enhanced */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 2rem;
}

.btn-buy-now {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    border: none;
    color: white;
    font-weight: 700;
    padding: 18px 35px;
    border-radius: 25px;
    font-size: 1.2rem;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.btn-buy-now::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-buy-now:hover::before {
    left: 100%;
}

.btn-buy-now:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.4);
    color: white;
}

.btn-add-cart {
    background: linear-gradient(135deg, var(--primary-color) 0%, #6610f2 100%);
    border: none;
    color: white;
    font-weight: 700;
    padding: 18px 35px;
    border-radius: 25px;
    font-size: 1.2rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-add-cart::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-add-cart:hover::before {
    left: 100%;
}

.btn-add-cart:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.4);
    color: white;
}

.btn-wishlist {
    background: white;
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    padding: 18px 35px;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
    font-size: 1.1rem;
}

.btn-wishlist:hover {
    background: var(--danger-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-container {
        padding: 1rem 0;
    }
    
    .product-gallery,
    .product-info {
        margin-bottom: 2rem;
    }
    
    .product-image-main {
        height: 300px;
    }
    
    .product-title {
        font-size: 1.8rem;
    }
    
    .price-current {
        font-size: 2.2rem;
    }
    
    .action-buttons {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
        z-index: 1000;
        flex-direction: row;
    }
    
    .btn-buy-now,
    .btn-add-cart {
        flex: 1;
        margin: 0 5px;
        padding: 15px 20px;
        font-size: 1rem;
    }
    
    .btn-wishlist {
        width: 60px;
        padding: 15px;
    }
}

/* Product Variants Styles */
.product-variants {
    margin-bottom: 2rem;
}

.variant-group {
    margin-bottom: 1.5rem;
}

.variant-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
    display: block;
    font-size: 1.1rem;
}

.variant-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.variant-option {
    padding: 0.75rem 1.25rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    position: relative;
    min-width: 50px;
    text-align: center;
    color: var(--dark-color);
}

.variant-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.variant-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.variant-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.color-variant {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    padding: 0;
    position: relative;
    overflow: hidden;
    border: 3px solid #dee2e6;
}

.color-variant::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
}

.color-variant.selected::after {
    opacity: 1;
}

.color-variant.selected {
    border-color: var(--primary-color);
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Enhanced Quantity Selector */
.quantity-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.quantity-label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    overflow: hidden;
    background: white;
}

.quantity-btn {
    background: #f8f9fa;
    border: none;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: white;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    border: none;
    width: 70px;
    height: 45px;
    text-align: center;
    font-weight: 600;
    background: white;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.quantity-input:focus {
    outline: none;
    background: #f8f9fa;
}
