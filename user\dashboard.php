<?php
/**
 * User Dashboard - لوحة تحكم العميل
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/Order.php';

// Require login
require_login();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$user_class = new User($db);
$order_class = new Order($db);

// Get user info
$user_info = $user_class->getUserById($_SESSION['user_id']);

// Get user orders
$recent_orders = $order_class->getUserOrders($_SESSION['user_id'], 1, 5);

// Get order statistics
$total_orders = $order_class->getTotalOrdersCount($_SESSION['user_id']);
$pending_orders = $order_class->getTotalOrdersCount($_SESSION['user_id'], 'pending');
$completed_orders = $order_class->getTotalOrdersCount($_SESSION['user_id'], 'delivered');

$page_title = "لوحة التحكم - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .sidebar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: #667eea;
            color: white;
        }
        
        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d4edda; color: #155724; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-shopping-bag me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-user-circle me-2"></i>
                        حسابي
                    </h5>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-bag me-2"></i>
                            طلباتي
                        </a>
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>
                            الملف الشخصي
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Welcome Section -->
                <div class="dashboard-card card mb-4">
                    <div class="card-body">
                        <h2 class="h4 mb-3">
                            مرحباً، <?php echo $user_info['first_name']; ?>! 👋
                        </h2>
                        <p class="text-muted mb-0">
                            مرحباً بك في لوحة التحكم الخاصة بك. يمكنك من هنا متابعة طلباتك وإدارة حسابك.
                        </p>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <h3><?php echo $total_orders; ?></h3>
                            <p class="mb-0">إجمالي الطلبات</p>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3><?php echo $pending_orders; ?></h3>
                            <p class="mb-0">طلبات قيد المعالجة</p>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3><?php echo $completed_orders; ?></h3>
                            <p class="mb-0">طلبات مكتملة</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="dashboard-card card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            الطلبات الأخيرة
                        </h5>
                        <a href="orders.php" class="btn btn-outline-primary btn-sm">
                            عرض جميع الطلبات
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_orders)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                            <th>المبلغ</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo $order['order_number']; ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo date('Y/m/d', strtotime($order['created_at'])); ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = 'status-' . $order['status'];
                                                    $status_text = '';
                                                    switch ($order['status']) {
                                                        case 'pending': $status_text = 'قيد الانتظار'; break;
                                                        case 'processing': $status_text = 'قيد المعالجة'; break;
                                                        case 'shipped': $status_text = 'تم الشحن'; break;
                                                        case 'delivered': $status_text = 'تم التسليم'; break;
                                                        case 'cancelled': $status_text = 'ملغي'; break;
                                                    }
                                                    ?>
                                                    <span class="order-status <?php echo $status_class; ?>">
                                                        <?php echo $status_text; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo format_price($order['total_amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i>
                                                        عرض
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                                <h5>لا توجد طلبات بعد</h5>
                                <p class="text-muted mb-3">لم تقم بإجراء أي طلبات حتى الآن</p>
                                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    ابدأ التسوق الآن
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-md-6 mb-3">
                        <div class="dashboard-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-3x text-primary mb-3"></i>
                                <h5>متابعة التسوق</h5>
                                <p class="text-muted">اكتشف منتجاتنا الجديدة والعروض المميزة</p>
                                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                                    تسوق الآن
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="dashboard-card card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-user-edit fa-3x text-success mb-3"></i>
                                <h5>تحديث الملف الشخصي</h5>
                                <p class="text-muted">قم بتحديث معلوماتك الشخصية وعنوان الشحن</p>
                                <a href="profile.php" class="btn btn-success">
                                    تحديث الملف
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
