<?php
/**
 * Admin Reports & Analytics - التقارير والإحصائيات
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Product.php';
require_once '../../classes/Order.php';
require_once '../../classes/User.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$order_class = new Order($db);
$user_class = new User($db);

// Get date range from filters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today

// Get comprehensive statistics
try {
    // Sales statistics
    $sales_stats = [
        'total_revenue' => $order_class->getTotalRevenue($date_from, $date_to),
        'total_orders' => $order_class->getTotalOrdersInPeriod($date_from, $date_to),
        'average_order_value' => $order_class->getAverageOrderValue($date_from, $date_to),
        'completed_orders' => $order_class->getCompletedOrdersCount($date_from, $date_to)
    ];
    
    // Product statistics
    $product_stats = [
        'total_products' => $product_class->getTotalCount(),
        'active_products' => $product_class->getActiveProductsCount(),
        'low_stock_products' => $product_class->getLowStockCount(),
        'top_selling' => $product_class->getTopSellingProducts(10)
    ];
    
    // User statistics
    $user_stats = [
        'total_users' => $user_class->getTotalCount(),
        'new_users_period' => $user_class->getNewUsersInPeriod($date_from, $date_to),
        'active_users' => $user_class->getActiveUsersCount()
    ];
    
    // Daily sales for chart
    $daily_sales = $order_class->getDailySales($date_from, $date_to);
    
} catch (Exception $e) {
    $error_message = "حدث خطأ في جلب البيانات: " . $e->getMessage();
}

$page_title = "التقارير والإحصائيات - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stats-icon.revenue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stats-icon.orders { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-icon.products { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-icon.users { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stats-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .table td {
            border: none;
            vertical-align: middle;
        }
        
        .table tbody tr {
            border-bottom: 1px solid #e9ecef;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .btn-export {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
        }
        
        .btn-export:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                    <h4 class="mb-0">التقارير والإحصائيات</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <button class="btn btn-export" onclick="exportReport()">
                        <i class="fas fa-download ms-2"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Date Filter -->
        <div class="filter-section">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter ms-2"></i>
                        فلترة
                    </button>
                </div>
                <div class="col-md-2">
                    <a href="index.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-refresh ms-2"></i>
                        إعادة تعيين
                    </a>
                </div>
                <div class="col-md-2">
                    <div class="text-muted small">
                        الفترة: <?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Main Statistics -->
        <div class="row">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon revenue">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-value"><?php echo format_price($sales_stats['total_revenue'] ?? 0); ?></div>
                    <p class="stats-label">إجمالي المبيعات</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon orders">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stats-value"><?php echo number_format($sales_stats['total_orders'] ?? 0); ?></div>
                    <p class="stats-label">إجمالي الطلبات</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon products">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stats-value"><?php echo number_format($product_stats['active_products'] ?? 0); ?></div>
                    <p class="stats-label">المنتجات النشطة</p>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-value"><?php echo number_format($user_stats['new_users_period'] ?? 0); ?></div>
                    <p class="stats-label">مستخدمين جدد</p>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-line ms-2"></i>
                        المبيعات اليومية
                    </h5>
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-pie ms-2"></i>
                        توزيع الطلبات
                    </h5>
                    <canvas id="ordersChart" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Products Table -->
        <div class="table-container">
            <h5 class="mb-4">
                <i class="fas fa-star ms-2"></i>
                المنتجات الأكثر مبيعاً
            </h5>

            <?php if (!empty($product_stats['top_selling'])): ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>المبيعات</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($product_stats['top_selling'] as $index => $product): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo $product['primary_image'] ? ASSETS_URL . '/images/products/' . $product['primary_image'] : ASSETS_URL . '/images/placeholder.svg'; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 class="rounded me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars(substr($product['name'], 0, 30)) . (strlen($product['name']) > 30 ? '...' : ''); ?></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo format_price($product['price']); ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo number_format($product['total_sold']); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo format_price($product['price'] * $product['total_sold']); ?></strong>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h6>لا توجد بيانات مبيعات</h6>
                    <p class="text-muted">لم يتم تسجيل أي مبيعات في الفترة المحددة</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Additional Statistics -->
        <div class="row">
            <div class="col-md-4">
                <div class="stats-card">
                    <h6 class="mb-3">
                        <i class="fas fa-shopping-bag ms-2"></i>
                        متوسط قيمة الطلب
                    </h6>
                    <div class="stats-value"><?php echo format_price($sales_stats['average_order_value'] ?? 0); ?></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="stats-card">
                    <h6 class="mb-3">
                        <i class="fas fa-check-circle ms-2"></i>
                        الطلبات المكتملة
                    </h6>
                    <div class="stats-value"><?php echo number_format($sales_stats['completed_orders'] ?? 0); ?></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="stats-card">
                    <h6 class="mb-3">
                        <i class="fas fa-exclamation-triangle ms-2"></i>
                        مخزون منخفض
                    </h6>
                    <div class="stats-value text-warning"><?php echo number_format($product_stats['low_stock_products'] ?? 0); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sales Chart
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: [<?php
                    if (isset($daily_sales) && !empty($daily_sales)) {
                        echo "'" . implode("','", array_column($daily_sales, 'date')) . "'";
                    } else {
                        echo "'لا توجد بيانات'";
                    }
                ?>],
                datasets: [{
                    label: 'المبيعات اليومية',
                    data: [<?php
                        if (isset($daily_sales) && !empty($daily_sales)) {
                            echo implode(',', array_column($daily_sales, 'total'));
                        } else {
                            echo '0';
                        }
                    ?>],
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });

        // Orders Chart
        const ordersCtx = document.getElementById('ordersChart').getContext('2d');
        const ordersChart = new Chart(ordersCtx, {
            type: 'doughnut',
            data: {
                labels: ['مكتملة', 'قيد المعالجة', 'ملغية'],
                datasets: [{
                    data: [
                        <?php echo $sales_stats['completed_orders'] ?? 0; ?>,
                        <?php echo ($sales_stats['total_orders'] ?? 0) - ($sales_stats['completed_orders'] ?? 0); ?>,
                        0
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Export function
        function exportReport() {
            const dateFrom = document.getElementById('date_from').value;
            const dateTo = document.getElementById('date_to').value;

            // Create export URL
            const exportUrl = `export.php?date_from=${dateFrom}&date_to=${dateTo}&format=excel`;

            // Open in new window or download
            window.open(exportUrl, '_blank');
        }

        // Auto-refresh every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
