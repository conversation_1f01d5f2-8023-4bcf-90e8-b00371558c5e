    <!-- Footer -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>متجر إلكتروني شامل لبيع أفضل المنتجات بأسعار مميزة وجودة عالية.</p>
                    
                    <!-- Currency Selector -->
                    <div class="mt-3">
                        <h6>العملة</h6>
                        <form method="POST" action="<?php echo BASE_URL; ?>includes/change_currency.php" class="d-inline"
                              onsubmit="return changeCurrency(this)">
                            <select name="currency" class="form-select form-select-sm d-inline-block w-auto" onchange="this.form.submit()">
                                <?php 
                                $current_currency = get_current_currency();
                                foreach (SUPPORTED_CURRENCIES as $code => $info): 
                                ?>
                                    <option value="<?php echo $code; ?>" <?php echo $code === $current_currency ? 'selected' : ''; ?>>
                                        <?php echo $info['symbol']; ?> - <?php echo $info['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    </div>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo BASE_URL; ?>" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="#" class="text-light text-decoration-none">من نحن</a></li>
                        <li><a href="#" class="text-light text-decoration-none">اتصل بنا</a></li>
                        <li><a href="<?php echo BASE_URL; ?>cart.php" class="text-light text-decoration-none">السلة</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h6>خدمة العملاء</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light text-decoration-none">سياسة الإرجاع</a></li>
                        <li><a href="#" class="text-light text-decoration-none">الشحن والتوصيل</a></li>
                        <li><a href="#" class="text-light text-decoration-none">الأسئلة الشائعة</a></li>
                        <li><a href="#" class="text-light text-decoration-none">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h6>تواصل معنا</h6>
                    <p><i class="fas fa-envelope me-2"></i> <?php echo ADMIN_EMAIL; ?></p>
                    <p><i class="fas fa-phone me-2"></i> +966 50 123 4567</p>
                    <div class="mt-3">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-whatsapp fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
                <small class="text-muted">
                    العملة الحالية: <?php echo SUPPORTED_CURRENCIES[get_current_currency()]['name']; ?>
                </small>
            </div>
        </div>
    </footer>

    <script>
        function changeCurrency(form) {
            // Submit form via AJAX to avoid page reload
            const formData = new FormData(form);

            fetch(form.action, {
                method: 'POST',
                body: formData
            }).then(response => {
                if (response.ok) {
                    // Reload page to show new currency
                    window.location.reload();
                }
            }).catch(error => {
                console.error('Error changing currency:', error);
                // Fallback to normal form submission
                return true;
            });

            return false; // Prevent normal form submission
        }
    </script>
