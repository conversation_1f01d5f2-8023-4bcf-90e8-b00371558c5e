/**
 * Professional Navbar Search - البحث الاحترافي لشريط التنقل
 * Advanced search functionality with suggestions and autocomplete
 */

class ProfessionalSearch {
    constructor() {
        this.searchInput = document.querySelector('.search-input');
        this.searchSuggestions = document.querySelector('.search-suggestions');
        this.searchForm = document.querySelector('.search-form');
        this.searchTimeout = null;
        this.currentIndex = -1;
        this.suggestions = [];
        
        this.init();
    }
    
    init() {
        if (!this.searchInput) return;
        
        this.bindEvents();
        this.setupKeyboardNavigation();
        this.setupClickOutside();
    }
    
    bindEvents() {
        // Input event for real-time search
        this.searchInput.addEventListener('input', (e) => {
            this.handleInput(e.target.value);
        });
        
        // Focus event
        this.searchInput.addEventListener('focus', () => {
            const query = this.searchInput.value.trim();
            if (query.length >= 2) {
                this.fetchSuggestions(query);
            }
        });
        
        // Form submit
        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => {
                this.handleSubmit(e);
            });
        }
    }
    
    setupKeyboardNavigation() {
        this.searchInput.addEventListener('keydown', (e) => {
            const suggestions = this.searchSuggestions?.querySelectorAll('.suggestion-item') || [];
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.currentIndex = this.currentIndex < suggestions.length - 1 ? 
                        this.currentIndex + 1 : 0;
                    this.updateActiveSuggestion(suggestions);
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    this.currentIndex = this.currentIndex > 0 ? 
                        this.currentIndex - 1 : suggestions.length - 1;
                    this.updateActiveSuggestion(suggestions);
                    break;
                    
                case 'Enter':
                    if (this.currentIndex >= 0 && suggestions[this.currentIndex]) {
                        e.preventDefault();
                        this.selectSuggestion(suggestions[this.currentIndex]);
                    }
                    break;
                    
                case 'Escape':
                    this.hideSuggestions();
                    this.searchInput.blur();
                    break;
                    
                case 'Tab':
                    if (this.currentIndex >= 0 && suggestions[this.currentIndex]) {
                        e.preventDefault();
                        this.searchInput.value = suggestions[this.currentIndex].dataset.value;
                        this.hideSuggestions();
                    }
                    break;
            }
        });
    }
    
    setupClickOutside() {
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.professional-search')) {
                this.hideSuggestions();
            }
        });
    }
    
    handleInput(value) {
        const query = value.trim();
        
        clearTimeout(this.searchTimeout);
        
        if (query.length >= 2) {
            this.searchTimeout = setTimeout(() => {
                this.fetchSuggestions(query);
            }, 300);
        } else {
            this.hideSuggestions();
        }
    }
    
    handleSubmit(e) {
        // If a suggestion is selected, use its URL
        if (this.currentIndex >= 0) {
            const suggestions = this.searchSuggestions?.querySelectorAll('.suggestion-item') || [];
            if (suggestions[this.currentIndex]) {
                e.preventDefault();
                this.selectSuggestion(suggestions[this.currentIndex]);
                return;
            }
        }
        
        // Add search analytics
        this.trackSearch(this.searchInput.value);
    }
    
    async fetchSuggestions(query) {
        try {
            const baseUrl = this.getBaseUrl();
            const response = await fetch(`${baseUrl}api/search-suggestions.php?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success && data.suggestions.length > 0) {
                this.suggestions = data.suggestions;
                this.showSuggestions(data.suggestions);
            } else {
                this.hideSuggestions();
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            this.hideSuggestions();
        }
    }
    
    showSuggestions(suggestions) {
        if (!this.searchSuggestions) return;
        
        this.searchSuggestions.innerHTML = suggestions.map((item, index) => `
            <div class="suggestion-item" 
                 data-value="${this.escapeHtml(item.name)}" 
                 data-url="${item.url || ''}"
                 data-type="${item.type || 'product'}"
                 data-index="${index}">
                <div class="suggestion-icon">
                    <i class="${item.icon || 'fas fa-search'}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-name">${this.highlightQuery(item.name, this.searchInput.value)}</div>
                    <div class="suggestion-category">${item.category || 'منتج'}</div>
                </div>
                ${item.price ? `<div class="suggestion-price">${item.price} ر.س</div>` : ''}
            </div>
        `).join('');
        
        this.searchSuggestions.style.display = 'block';
        this.currentIndex = -1;
        
        // Add click handlers
        this.searchSuggestions.querySelectorAll('.suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectSuggestion(item);
            });
            
            item.addEventListener('mouseenter', () => {
                this.currentIndex = index;
                this.updateActiveSuggestion(this.searchSuggestions.querySelectorAll('.suggestion-item'));
            });
        });
    }
    
    hideSuggestions() {
        if (this.searchSuggestions) {
            this.searchSuggestions.style.display = 'none';
            this.currentIndex = -1;
        }
    }
    
    updateActiveSuggestion(suggestions) {
        suggestions.forEach((item, i) => {
            item.classList.toggle('active', i === this.currentIndex);
        });
        
        // Scroll active item into view
        if (this.currentIndex >= 0 && suggestions[this.currentIndex]) {
            suggestions[this.currentIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }
    
    selectSuggestion(item) {
        const url = item.dataset.url;
        const value = item.dataset.value;
        const type = item.dataset.type;
        
        // Track selection
        this.trackSuggestionClick(value, type);
        
        if (url) {
            window.location.href = url;
        } else {
            this.searchInput.value = value;
            this.hideSuggestions();
            this.searchForm.submit();
        }
    }
    
    highlightQuery(text, query) {
        if (!query) return this.escapeHtml(text);
        
        const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedQuery})`, 'gi');
        return this.escapeHtml(text).replace(regex, '<mark>$1</mark>');
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    getBaseUrl() {
        return document.querySelector('meta[name="base-url"]')?.content || 
               window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
    }
    
    trackSearch(query) {
        // Analytics tracking for search
        if (typeof gtag !== 'undefined') {
            gtag('event', 'search', {
                search_term: query
            });
        }
        
        // Send to backend for analytics
        fetch(`${this.getBaseUrl()}api/analytics.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'search',
                query: query,
                timestamp: new Date().toISOString()
            })
        }).catch(error => console.error('Analytics error:', error));
    }
    
    trackSuggestionClick(value, type) {
        // Analytics tracking for suggestion clicks
        if (typeof gtag !== 'undefined') {
            gtag('event', 'suggestion_click', {
                suggestion_text: value,
                suggestion_type: type
            });
        }
    }
}

// Initialize professional search when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.professionalSearch = new ProfessionalSearch();
});

// Export for external use
window.ProfessionalSearch = ProfessionalSearch;
