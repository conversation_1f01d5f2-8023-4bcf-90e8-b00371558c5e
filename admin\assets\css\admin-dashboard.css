/* Admin Dashboard Styles - أنماط لوحة التحكم */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --sidebar-bg: #2c3e50;
    --sidebar-hover: #34495e;
    --text-light: #bdc3c7;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --dark-color: #2c3e50;
    --light-bg: #f8f9fa;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 12px;
}

/* Body and Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--light-bg);
    margin: 0;
    padding: 0;
}

/* Sidebar Styles */
.admin-sidebar {
    background: linear-gradient(135deg, var(--sidebar-bg) 0%, #34495e 100%);
    min-height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1050;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.admin-sidebar.show {
    transform: translateX(0);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-header h4 {
    color: white;
    margin: 0;
    font-weight: 600;
}

.sidebar-header .subtitle {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav .nav-item {
    margin: 0.25rem 0;
}

.sidebar-nav .nav-link {
    color: var(--text-light);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.sidebar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-nav .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.sidebar-nav .nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: white;
}

.sidebar-nav .nav-link i {
    width: 20px;
    margin-left: 1rem;
    text-align: center;
}

.sidebar-nav .badge {
    margin-right: auto;
    font-size: 0.75rem;
}

/* Main Content */
.main-content {
    margin-right: 0;
    transition: margin-right 0.3s ease;
    min-height: 100vh;
}

.main-content.sidebar-open {
    margin-right: 280px;
}

/* Top Navigation */
.top-navbar {
    background: white;
    box-shadow: var(--card-shadow);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1040;
}

.navbar-brand {
    font-weight: 700;
    color: var(--dark-color) !important;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: var(--light-bg);
    transform: scale(1.1);
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-icon.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.stat-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.stat-icon.info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.stat-icon.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-change {
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Charts */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-bg);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

/* Tables */
.data-table {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.table-header {
    background: var(--light-bg);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.table {
    margin: 0;
}

.table th {
    background: var(--light-bg);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 100%;
        transform: translateX(100%);
    }
    
    .main-content.sidebar-open {
        margin-right: 0;
    }
    
    .top-navbar {
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .dashboard-card {
        padding: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utilities */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.btn-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

/* Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}
