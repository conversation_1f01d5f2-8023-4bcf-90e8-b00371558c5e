<?php
/**
 * Settings Page - صفحة الإعدادات
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check admin privileges
require_admin();

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_currency'])) {
        $currency = $_POST['currency'];
        if (set_current_currency($currency)) {
            $success_message = "تم تحديث العملة بنجاح!";
        } else {
            $error_message = "عملة غير صحيحة!";
        }
    }
}

$current_currency = get_current_currency();
$page_title = "إعدادات الموقع";
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    
    <style>
        .settings-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .currency-option {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .currency-option:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        
        .currency-option.selected {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        
        .currency-symbol {
            font-size: 1.5rem;
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-shopping-bag ms-2 ms-1"></i>لوحة تحكم <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../auth/logout.php">
                    <i class="fas fa-sign-out-alt ms-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2">
                <div class="list-group">
                    <a href="index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt ms-2 ms-1"></i>الرئيسية
                    </a>
                    <a href="products/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-box ms-2 ms-1"></i>المنتجات
                    </a>
                    <a href="categories/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags ms-2 ms-1"></i>التصنيفات
                    </a>
                    <a href="orders/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart ms-2 ms-1"></i>الطلبات
                    </a>
                    <a href="settings.php" class="list-group-item list-group-item-action active">
                        <i class="fas fa-cog ms-2 ms-1"></i>الإعدادات
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-cog ms-2 ms-1"></i>إعدادات الموقع
                    </h2>
                </div>

                <!-- Messages -->
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle ms-2 ms-1"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle ms-2 ms-1"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Currency Settings -->
                <div class="settings-section">
                    <h5 class="mb-4">
                        <i class="fas fa-money-bill-wave ms-2 ms-1"></i>إعدادات العملة
                    </h5>
                    
                    <form method="POST">
                        <div class="row">
                            <?php foreach (SUPPORTED_CURRENCIES as $code => $info): ?>
                                <div class="col-md-6 col-lg-4">
                                    <div class="currency-option <?php echo $code === $current_currency ? 'selected' : ''; ?>" 
                                         onclick="selectCurrency('<?php echo $code; ?>')">
                                        <div class="d-flex align-items-center">
                                            <div class="currency-symbol ms-3">
                                                <?php echo $info['symbol']; ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo $info['name']; ?></div>
                                                <small class="text-muted"><?php echo $code; ?></small>
                                            </div>
                                            <div class="ms-auto">
                                                <input type="radio" name="currency" value="<?php echo $code; ?>" 
                                                       <?php echo $code === $current_currency ? 'checked' : ''; ?> 
                                                       class="form-check-input">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" name="update_currency" class="btn btn-primary">
                                <i class="fas fa-save ms-2 ms-1"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Preview Section -->
                <div class="settings-section">
                    <h5 class="mb-4">
                        <i class="fas fa-eye ms-2 ms-1"></i>معاينة الأسعار
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title">منتج تجريبي</h6>
                                    <div class="h4 text-primary" id="price-preview">
                                        <?php echo format_price(299.99, $current_currency); ?>
                                    </div>
                                    <small class="text-muted">السعر بالعملة المحددة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function selectCurrency(currency) {
            // Remove selected class from all options
            document.querySelectorAll('.currency-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selected class to clicked option
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.querySelector(`input[value="${currency}"]`).checked = true;
            
            // Update price preview
            updatePricePreview(currency);
        }
        
        function updatePricePreview(currency) {
            const currencies = {
                'SAR': 'ر.س',
                'EGP': 'ج.م',
                'USD': '$',
                'EUR': '€'
            };
            
            const symbol = currencies[currency] || 'ر.س';
            const price = (299.99).toLocaleString('ar-SA', {minimumFractionDigits: 2});
            
            document.getElementById('price-preview').textContent = price + ' ' + symbol;
        }
    </script>
</body>
</html>
