<?php
/**
 * Login Page - صفحة تسجيل الدخول
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';

// Redirect if already logged in
if (is_logged_in()) {
    if (is_admin()) {
        header('Location: ' . BASE_URL . 'admin/');
    } else {
        header('Location: ' . BASE_URL . 'user/dashboard.php');
    }
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize user class
$user = new User($db);

$errors = array();
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    // Validation
    if (empty($username)) {
        $errors[] = "اسم المستخدم أو البريد الإلكتروني مطلوب";
    }
    
    if (empty($password)) {
        $errors[] = "كلمة المرور مطلوبة";
    }
    
    if (empty($errors)) {
        $login_result = $user->login($username, $password);
        
        if ($login_result) {
            // Set remember me cookie if requested
            if ($remember_me) {
                setcookie('remember_user', $username, time() + (30 * 24 * 60 * 60), '/'); // 30 days
            }
            
            // Redirect based on user role
            if ($login_result['role'] === 'admin') {
                header('Location: ' . BASE_URL . 'admin/');
            } else {
                $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : BASE_URL . 'user/dashboard.php';
                header('Location: ' . $redirect);
            }
            exit();
        } else {
            $errors[] = "اسم المستخدم أو كلمة المرور غير صحيحة";
        }
    }
}

// Check for registration success message
if (isset($_GET['registered']) && $_GET['registered'] === 'success') {
    $success_message = "تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.";
}

$page_title = "تسجيل الدخول - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .auth-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .social-login {
            border-top: 1px solid #dee2e6;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border-right: none;
        }
        
        .form-control {
            border-left: none;
        }
        
        .auth-links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .auth-links a {
            color: #667eea;
            text-decoration: none;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card">
                    <!-- Header -->
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="fas fa-shopping-bag me-2"></i>
                            <?php echo SITE_NAME; ?>
                        </h3>
                        <p class="mb-0 mt-2">مرحباً بك مرة أخرى</p>
                    </div>
                    
                    <!-- Body -->
                    <div class="auth-body">
                        <!-- Success Message -->
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Error Messages -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php foreach ($errors as $error): ?>
                                    <div><?php echo $error; ?></div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Login Form -->
                        <form method="POST" id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? $_COOKIE['remember_user'] ?? ''); ?>" 
                                           required autofocus>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me" 
                                       <?php echo isset($_COOKIE['remember_user']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="remember_me">
                                    تذكرني
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>
                        
                        <!-- Links -->
                        <div class="auth-links">
                            <p class="mb-2">
                                <a href="forgot-password.php">نسيت كلمة المرور؟</a>
                            </p>
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="register.php">إنشاء حساب جديد</a>
                            </p>
                        </div>
                        
                        <!-- Divider -->
                        <div class="social-login">
                            <div class="text-center">
                                <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Demo Accounts Info -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle me-2"></i>
                            حسابات تجريبية
                        </h6>
                        <div class="row">
                            <div class="col-6">
                                <strong>المدير:</strong><br>
                                <small><EMAIL></small><br>
                                <small>password</small>
                            </div>
                            <div class="col-6">
                                <strong>عميل:</strong><br>
                                <small><EMAIL></small><br>
                                <small>password</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
            }
        });
        
        // Auto-fill demo credentials
        function fillDemo(type) {
            if (type === 'admin') {
                document.getElementById('username').value = '<EMAIL>';
                document.getElementById('password').value = 'password';
            } else {
                document.getElementById('username').value = '<EMAIL>';
                document.getElementById('password').value = 'password';
            }
        }
        
        // Add click handlers for demo accounts
        document.addEventListener('DOMContentLoaded', function() {
            const demoInfo = document.querySelector('.card-body');
            if (demoInfo) {
                demoInfo.addEventListener('click', function(e) {
                    if (e.target.closest('.col-6:first-child')) {
                        fillDemo('admin');
                    } else if (e.target.closest('.col-6:last-child')) {
                        fillDemo('customer');
                    }
                });
            }
        });
    </script>
</body>
</html>
