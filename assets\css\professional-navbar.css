/**
 * Professional Navbar Styles - أنماط شريط التنقل الاحترافي
 * Enhanced responsive navigation with modern design
 */

/* Professional Navbar Base */
.professional-navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    min-height: 80px;
    position: relative;
    z-index: 1030;
}

.professional-navbar.scrolled {
    min-height: 70px;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.95) 0%, rgba(42, 82, 152, 0.95) 100%) !important;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15);
}

/* Professional Brand */
.professional-brand {
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-brand:hover {
    transform: scale(1.02);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.brand-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.brand-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.professional-brand:hover .brand-icon::before {
    opacity: 1;
    animation: shine 0.6s ease;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
    background: linear-gradient(45deg, #fff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-tagline {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
    margin-top: -2px;
}

.professional-brand:hover .brand-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Professional Toggler */
.professional-toggler {
    border: none;
    padding: 8px;
    width: 40px;
    height: 40px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.professional-toggler:focus {
    box-shadow: none;
    background: rgba(255, 255, 255, 0.2);
    outline: 2px solid rgba(255, 255, 255, 0.5);
}

.professional-toggler:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
}

.professional-toggler span {
    display: block;
    width: 20px;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 1px;
}

.professional-toggler.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.professional-toggler.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(20px);
}

.professional-toggler.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Professional Navigation Links */
.professional-nav .nav-link,
.professional-nav-right .nav-link {
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    margin: 0 4px;
    padding: 10px 16px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none;
    overflow: hidden;
}

.professional-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
    transition: all 0.3s ease;
    z-index: -1;
}

.professional-link:hover::before {
    left: 0;
}

.professional-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    color: white !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.professional-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.professional-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 1px;
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.professional-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-size: 14px;
    white-space: nowrap;
    font-weight: inherit;
}

/* Special Link Styles */
.offers-link {
    position: relative;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(255, 107, 107, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
    }
}

.offers-badge {
    position: absolute;
    top: -5px;
    right: -10px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    font-size: 9px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    animation: bounce 2s infinite;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.login-link,
.register-link {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.login-link:hover,
.register-link:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
}

.register-link {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: transparent;
}

.register-link:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px) scale(1.02);
}

/* Professional Search */
.professional-search {
    position: relative;
    z-index: 1000;
}

.search-container {
    position: relative;
    width: 300px;
    max-width: 100%;
}

.search-input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.search-input::placeholder {
    color: #666;
    font-style: italic;
}

.search-input:focus {
    outline: none;
    background: white;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
    border: 2px solid rgba(102, 126, 234, 0.3);
    padding-left: 18px;
    padding-right: 48px;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.search-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    margin-top: 5px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 4px 8px;
    position: relative;
    overflow: hidden;
}

.suggestion-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    z-index: -1;
}

.suggestion-item:hover::before,
.suggestion-item.active::before {
    left: 0;
}

.suggestion-item:hover,
.suggestion-item.active {
    color: white;
    transform: translateX(5px);
}

.suggestion-icon {
    width: 30px;
    height: 30px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    font-size: 12px;
    color: #666;
    transition: all 0.3s ease;
}

.suggestion-item:hover .suggestion-icon,
.suggestion-item.active .suggestion-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.1);
}

.suggestion-content {
    flex: 1;
}

.suggestion-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
    line-height: 1.3;
}

.suggestion-category {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.2;
}

/* Cart and User Elements */
.cart-icon {
    position: relative;
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
    animation: pulse 2s infinite;
    border: 2px solid white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.professional-link:hover .user-avatar {
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.user-name {
    font-weight: 500;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Professional Dropdowns */
.professional-dropdown {
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
    padding: 10px 0;
    min-width: 250px;
    animation: fadeInDown 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.professional-dropdown-item {
    border-radius: 10px;
    margin: 2px 10px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.professional-dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    z-index: -1;
}

.professional-dropdown-item:hover::before {
    left: 0;
}

.professional-dropdown-item:hover {
    color: white;
    transform: translateX(-5px);
}

.professional-dropdown-item.admin-link::before {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.professional-dropdown-item.logout-link::before {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.professional-dropdown-item i {
    width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.professional-dropdown-item:hover i {
    transform: scale(1.1);
}

/* Mega Menu Styles */
.mega-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    max-width: 90vw;
}

.mega-menu {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
    padding: 30px;
    animation: fadeInUp 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.mega-menu-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.mega-menu-title {
    color: #333;
    font-weight: 600;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mega-menu-title i {
    color: #667eea;
}

.mega-menu-content {
    position: relative;
}

.mega-menu-item {
    display: block;
    padding: 15px;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #333;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
    margin-bottom: 10px;
}

.mega-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: all 0.3s ease;
    z-index: -1;
}

.mega-menu-item:hover::before {
    left: 0;
}

.mega-menu-item:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    border-color: transparent;
}

.mega-menu-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.mega-item-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mega-menu-item:hover .mega-item-icon {
    background: white;
    color: #667eea;
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 15px rgba(102, 126, 234, 0.4);
}

.mega-item-content {
    position: relative;
    z-index: 2;
}

.mega-item-content h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.3;
}

.mega-item-content p {
    margin: 0;
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.4;
}

.mega-item-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    z-index: 3;
}

.mega-menu-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px solid #f8f9fa;
    text-align: center;
}

.mega-menu-footer .btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mega-menu-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.3);
}

/* User Dropdown Enhancements */
.user-dropdown-menu {
    min-width: 280px;
    max-width: 320px;
}

.dropdown-header .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 16px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    margin: 0 10px 10px 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    border: 2px solid white;
}

.user-details {
    flex: 1;
}

.user-details h6 {
    margin: 0;
    color: #333;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.2;
}

.user-details small {
    color: #6c757d;
    font-size: 12px;
    display: block;
    margin-top: 2px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Loading Animation */
.navbar-loading {
    position: relative;
    overflow: hidden;
}

.navbar-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Navbar Notifications */
.navbar-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 16px 20px;
    z-index: 9999;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 350px;
    border-left: 4px solid #007bff;
    backdrop-filter: blur(10px);
}

.navbar-notification.success {
    border-left-color: #28a745;
}

.navbar-notification.error {
    border-left-color: #dc3545;
}

.navbar-notification.warning {
    border-left-color: #ffc107;
}

.navbar-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-content i {
    font-size: 18px;
}

.notification-content i.fa-check-circle {
    color: #28a745;
}

.notification-content i.fa-exclamation-circle {
    color: #dc3545;
}

.notification-content i.fa-info-circle {
    color: #007bff;
}

.notification-content span {
    font-weight: 500;
    color: #333;
}

/* Professional loading spinner */
.navbar-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-left: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Responsive Design */
@media (max-width: 991px) {
    .professional-navbar {
        min-height: 70px;
        padding: 0.5rem 0;
    }

    .container-fluid {
        padding-left: 20px;
        padding-right: 20px;
    }

    .brand-container {
        gap: 8px;
    }

    .brand-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .brand-name {
        font-size: 1.2rem;
    }

    .brand-tagline {
        font-size: 0.7rem;
    }

    .professional-search {
        margin: 15px 0;
        width: 100%;
        order: 3;
    }

    .search-container {
        width: 100%;
    }

    .navbar-nav {
        text-align: right;
        margin-top: 1rem;
        width: 100%;
    }

    .professional-nav,
    .professional-nav-right {
        width: 100%;
    }

    .professional-nav .nav-link,
    .professional-nav-right .nav-link {
        padding: 12px 16px;
        margin: 2px 0;
        border-radius: 10px;
        justify-content: flex-start;
    }

    .nav-text {
        display: inline !important;
    }

    /* Mobile Mega Menu */
    .mega-dropdown .dropdown-menu {
        position: static !important;
        transform: none !important;
        width: 100%;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: none;
        margin-top: 8px;
    }

    .mega-menu {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: none;
        padding: 15px;
        border-radius: 10px;
    }

    .mega-menu-title {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
    }

    .mega-menu-item {
        color: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 8px;
        background: rgba(255, 255, 255, 0.05);
    }

    .mega-menu-item:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(0);
    }

    .mega-item-content h6,
    .mega-item-content p {
        color: inherit;
    }

    .mega-item-icon {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);
    }

    .mega-menu-item:hover .mega-item-icon {
        background: rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* Mobile Dropdowns */
    .professional-dropdown {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: none;
        border-radius: 10px;
        margin-top: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .professional-dropdown-item {
        color: rgba(255, 255, 255, 0.9);
        margin: 2px 5px;
    }

    .professional-dropdown-item:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateX(0);
    }

    .user-dropdown-menu .dropdown-header .user-info {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .user-dropdown-menu .user-details h6 {
        color: rgba(255, 255, 255, 0.9);
    }

    .user-dropdown-menu .user-details small {
        color: rgba(255, 255, 255, 0.7);
    }

    .user-avatar-large {
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Mobile Search Adjustments */
    .search-input {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 14px 50px 14px 20px;
    }

    .search-btn {
        width: 40px;
        height: 40px;
        right: 3px;
    }

    /* Mobile Notifications */
    .navbar-notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .navbar-notification.show {
        transform: translateY(0);
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .brand-name {
        font-size: 1.1rem;
    }

    .brand-tagline {
        display: none;
    }

    .brand-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .professional-nav .nav-link,
    .professional-nav-right .nav-link {
        padding: 10px 12px;
        font-size: 14px;
    }

    .nav-icon {
        font-size: 14px;
        width: 18px;
    }

    .mega-menu .row .col-md-3 {
        margin-bottom: 10px;
    }

    .mega-menu {
        padding: 15px 10px;
    }

    .mega-menu-item {
        padding: 12px;
    }

    .mega-item-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .search-container {
        max-width: 100%;
    }

    .user-dropdown-menu {
        min-width: 250px;
    }

    .dropdown-header .user-info {
        padding: 12px;
        gap: 10px;
    }

    .user-avatar-large {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Tablet Responsive */
@media (min-width: 768px) and (max-width: 991px) {
    .search-container {
        width: 250px;
    }

    .mega-menu {
        width: 500px;
    }

    .professional-nav .nav-link,
    .professional-nav-right .nav-link {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1200px) {
    .container-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }

    .search-container {
        width: 350px;
    }

    .mega-menu {
        width: 700px;
    }

    .professional-nav .nav-link,
    .professional-nav-right .nav-link {
        padding: 12px 20px;
        margin: 0 6px;
    }
}

/* Enhanced focus states for accessibility */
.professional-link:focus-visible,
.search-input:focus-visible,
.search-btn:focus-visible,
.professional-dropdown-item:focus-visible {
    outline: 2px solid #fff;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .professional-navbar {
        background: #000 !important;
        border-bottom: 2px solid #fff;
    }

    .professional-link {
        color: #fff !important;
        border: 1px solid transparent;
    }

    .professional-link:hover,
    .professional-link:focus {
        border-color: #fff;
        background: #333 !important;
    }

    .search-input {
        border: 2px solid #fff;
        background: #000;
        color: #fff;
    }

    .search-btn {
        background: #fff;
        color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .professional-navbar,
    .professional-link,
    .dropdown-menu,
    .search-input,
    .search-btn,
    .cart-badge,
    .offers-badge,
    .brand-icon,
    .mega-menu-item,
    .suggestion-item {
        transition: none !important;
        animation: none !important;
    }

    .professional-link:hover,
    .mega-menu-item:hover,
    .suggestion-item:hover {
        transform: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .search-input {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-suggestions {
        background: rgba(30, 30, 30, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .suggestion-item {
        color: rgba(255, 255, 255, 0.9);
    }

    .suggestion-icon {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.7);
    }

    .professional-dropdown {
        background: rgba(30, 30, 30, 0.98);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .professional-dropdown-item {
        color: rgba(255, 255, 255, 0.9);
    }

    .mega-menu {
        background: rgba(30, 30, 30, 0.98);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mega-menu-title {
        color: white;
    }

    .mega-menu-item {
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .user-dropdown-menu .dropdown-header .user-info {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .navbar-notification {
        background: rgba(30, 30, 30, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .notification-content span {
        color: rgba(255, 255, 255, 0.9);
    }
}

/* Print Styles */
@media print {
    .professional-navbar {
        display: none !important;
    }
}

/* Performance Optimizations */
.professional-navbar * {
    box-sizing: border-box;
}

.professional-navbar .dropdown-menu {
    will-change: transform, opacity;
}

.professional-link {
    will-change: transform, background-color;
}

/* RTL Specific Adjustments */
[dir="rtl"] .search-btn {
    right: auto;
    left: 5px;
}

[dir="rtl"] .cart-badge {
    right: auto;
    left: -8px;
}

[dir="rtl"] .offers-badge {
    right: auto;
    left: 0px;
}

[dir="rtl"] .mega-item-badge {
    right: auto;
    left: 10px;
}

[dir="rtl"] .professional-dropdown-item:hover {
    transform: translateX(5px);
}

[dir="rtl"] .suggestion-item:hover {
    transform: translateX(-5px);
}

[dir="rtl"] .suggestion-icon {
    margin-left: 0;
    margin-right: 12px;
}
