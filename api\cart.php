<?php
/**
 * Cart API - واجهة برمجة تطبيقات السلة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/Product.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize product class
$product_class = new Product($db);

$response = array('success' => false, 'message' => '');

// Debug logging
error_log('Cart API called - Method: ' . $_SERVER['REQUEST_METHOD']);
error_log('POST data: ' . print_r($_POST, true));
error_log('GET data: ' . print_r($_GET, true));

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        error_log('POST Action: ' . $action);
        
        switch ($action) {
            case 'add':
            case 'add_to_cart':
                $product_id = (int)($_POST['product_id'] ?? 0);
                $variant_id = !empty($_POST['variant_id']) ? (int)$_POST['variant_id'] : null;
                $quantity = (int)($_POST['quantity'] ?? 1);
                
                if ($product_id > 0 && $quantity > 0) {
                    // Verify product exists and is active
                    $product = $product_class->getProductById($product_id);

                    if ($product) {
                        // Check if product is active (handle both 1/0 and true/false)
                        $is_active = isset($product['is_active']) ?
                                   ($product['is_active'] == 1 || $product['is_active'] === true) : true;

                        if ($is_active) {
                        // Check stock availability
                        $available_stock = $product['stock_quantity'];
                        
                        if ($variant_id) {
                            $variants = $product_class->getProductVariants($product_id);
                            foreach ($variants as $variant) {
                                if ($variant['id'] == $variant_id) {
                                    $available_stock = $variant['stock_quantity'];
                                    break;
                                }
                            }
                        }
                        
                        if ($available_stock >= $quantity) {
                            if (add_to_cart($product_id, $variant_id, $quantity)) {
                                $response['success'] = true;
                                $response['message'] = 'تم إضافة المنتج إلى السلة بنجاح';
                                $response['cart_count'] = get_cart_count();
                            } else {
                                $response['message'] = 'حدث خطأ أثناء إضافة المنتج إلى السلة';
                            }
                        } else {
                            $response['message'] = 'الكمية المطلوبة غير متوفرة في المخزون';
                        }
                        } else {
                            $response['message'] = 'المنتج غير متوفر أو غير نشط';
                        }
                    } else {
                        $response['message'] = 'المنتج غير موجود';
                    }
                } else {
                    $response['message'] = 'بيانات غير صحيحة';
                }
                break;
                
            case 'remove_from_cart':
                $cart_key = $_POST['cart_key'] ?? '';
                
                if (!empty($cart_key) && isset($_SESSION['cart'][$cart_key])) {
                    unset($_SESSION['cart'][$cart_key]);
                    $response['success'] = true;
                    $response['message'] = 'تم إزالة المنتج من السلة';
                    $response['cart_count'] = get_cart_count();
                } else {
                    $response['message'] = 'المنتج غير موجود في السلة';
                }
                break;
                
            case 'update_quantity':
                $cart_key = $_POST['cart_key'] ?? '';
                $quantity = (int)($_POST['quantity'] ?? 0);
                
                if (!empty($cart_key) && $quantity > 0 && isset($_SESSION['cart'][$cart_key])) {
                    // Verify stock availability
                    $parts = explode('_', $cart_key);
                    $product_id = (int)$parts[0];
                    $variant_id = isset($parts[1]) ? (int)$parts[1] : null;
                    
                    $product = $product_class->getProductById($product_id);
                    if ($product) {
                        $available_stock = $product['stock_quantity'];
                        
                        if ($variant_id) {
                            $variants = $product_class->getProductVariants($product_id);
                            foreach ($variants as $variant) {
                                if ($variant['id'] == $variant_id) {
                                    $available_stock = $variant['stock_quantity'];
                                    break;
                                }
                            }
                        }
                        
                        if ($available_stock >= $quantity) {
                            $_SESSION['cart'][$cart_key] = $quantity;
                            $response['success'] = true;
                            $response['message'] = 'تم تحديث الكمية';
                            $response['cart_count'] = get_cart_count();
                        } else {
                            $response['message'] = 'الكمية المطلوبة غير متوفرة في المخزون';
                        }
                    } else {
                        $response['message'] = 'المنتج غير موجود';
                    }
                } else {
                    $response['message'] = 'بيانات غير صحيحة';
                }
                break;
                
            case 'clear_cart':
                clear_cart();
                $response['success'] = true;
                $response['message'] = 'تم مسح السلة';
                $response['cart_count'] = 0;
                break;
                
            default:
                $response['message'] = 'إجراء غير صحيح: ' . $action . ' (POST)';
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_count':
                $response['success'] = true;
                $response['count'] = get_cart_count();
                break;
                
            case 'get_totals':
                $cart_totals = calculate_cart_totals();
                $response['success'] = true;
                $response['subtotal'] = $cart_totals['subtotal'];
                $response['tax'] = $cart_totals['tax'];
                $response['shipping'] = $cart_totals['shipping'];
                $response['total'] = $cart_totals['total'];
                $response['subtotal_formatted'] = format_price($cart_totals['subtotal']);
                $response['tax_formatted'] = format_price($cart_totals['tax']);
                $response['shipping_formatted'] = format_price($cart_totals['shipping']);
                $response['total_formatted'] = format_price($cart_totals['total']);
                break;
                
            case 'get_items':
                $cart_items = get_cart_items_with_details($product_class);
                $response['success'] = true;
                $response['items'] = $cart_items;
                $response['count'] = count($cart_items);
                break;
                
            default:
                $response['message'] = 'إجراء غير صحيح';
        }
    } else {
        $response['message'] = 'طريقة طلب غير مدعومة';
    }
    
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ في الخادم: ' . $e->getMessage();
    error_log('Cart API Error: ' . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

/**
 * Calculate cart totals
 * حساب إجماليات السلة
 */
function calculate_cart_totals() {
    global $product_class;
    
    $subtotal = 0;
    $shipping_cost = 25.00;
    $tax_rate = 0.15;
    
    if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $cart_key => $quantity) {
            $parts = explode('_', $cart_key);
            $product_id = (int)$parts[0];
            $variant_id = isset($parts[1]) ? (int)$parts[1] : null;
            
            $product = $product_class->getProductById($product_id);
            if ($product) {
                $item_price = $product['sale_price'] ?: $product['price'];
                
                if ($variant_id) {
                    $variants = $product_class->getProductVariants($product_id);
                    foreach ($variants as $variant) {
                        if ($variant['id'] == $variant_id) {
                            $item_price = $variant['price'] ?: $item_price;
                            break;
                        }
                    }
                }
                
                $subtotal += $item_price * $quantity;
            }
        }
    }
    
    // Free shipping for orders over 200 SAR
    if ($subtotal >= 200) {
        $shipping_cost = 0;
    }
    
    $tax_amount = $subtotal * $tax_rate;
    $total = $subtotal + $shipping_cost + $tax_amount;
    
    return array(
        'subtotal' => $subtotal,
        'shipping' => $shipping_cost,
        'tax' => $tax_amount,
        'total' => $total
    );
}

/**
 * Get cart items with product details
 * الحصول على عناصر السلة مع تفاصيل المنتجات
 */
function get_cart_items_with_details($product_class) {
    $cart_items = array();
    
    if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $cart_key => $quantity) {
            $parts = explode('_', $cart_key);
            $product_id = (int)$parts[0];
            $variant_id = isset($parts[1]) ? (int)$parts[1] : null;
            
            $product = $product_class->getProductById($product_id);
            if ($product) {
                $item_price = $product['sale_price'] ?: $product['price'];
                $item_total = $item_price * $quantity;
                
                // Get primary image
                $images = $product_class->getProductImages($product_id);
                $primary_image = !empty($images) ? $images[0]['image_path'] : null;
                
                // Get variant details if applicable
                $variant_info = '';
                if ($variant_id) {
                    $variants = $product_class->getProductVariants($product_id);
                    foreach ($variants as $variant) {
                        if ($variant['id'] == $variant_id) {
                            $variant_info = $variant['attributes'];
                            $item_price = $variant['price'] ?: $item_price;
                            $item_total = $item_price * $quantity;
                            break;
                        }
                    }
                }
                
                $cart_items[] = array(
                    'cart_key' => $cart_key,
                    'product_id' => $product_id,
                    'variant_id' => $variant_id,
                    'name' => $product['name'],
                    'price' => $item_price,
                    'quantity' => $quantity,
                    'total' => $item_total,
                    'image' => $primary_image,
                    'variant_info' => $variant_info,
                    'stock_quantity' => $product['stock_quantity']
                );
            }
        }
    }
    
    return $cart_items;
}
?>
