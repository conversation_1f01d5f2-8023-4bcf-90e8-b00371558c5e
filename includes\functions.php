<?php
/**
 * Common Functions
 * الدوال المشتركة
 */

/**
 * Sanitize input data
 * تنظيف البيانات المدخلة
 */
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate email address
 * التحقق من صحة البريد الإلكتروني
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Saudi format)
 * التحقق من صحة رقم الهاتف (الصيغة السعودية)
 */
function validate_phone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/\D/', '', $phone);

    // Check if it matches Saudi phone number patterns
    return preg_match('/^(966|0)?5[0-9]{8}$/', $phone);
}

/**
 * Generate CSRF token
 * إنشاء رمز CSRF
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * التحقق من رمز CSRF
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user is logged in
 * التحقق من تسجيل دخول المستخدم
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 * التحقق من صلاحيات الإدارة
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Redirect to login if not authenticated
 * إعادة توجيه لصفحة تسجيل الدخول
 */
function require_login() {
    if (!is_logged_in()) {
        header('Location: ' . BASE_URL . 'auth/login.php');
        exit();
    }
}

/**
 * Require admin privileges
 * طلب صلاحيات الإدارة
 */
function require_admin() {
    require_login();
    if (!is_admin()) {
        header('Location: ' . BASE_URL . 'index.php');
        exit();
    }
}

/**
 * Format price with currency
 * تنسيق السعر مع العملة
 */
function format_price($price, $currency = null) {
    if ($currency === null) {
        $currency = DEFAULT_CURRENCY;
    }

    $currencies = SUPPORTED_CURRENCIES;
    $symbol = isset($currencies[$currency]) ? $currencies[$currency]['symbol'] : 'ر.س';
    return number_format($price, 2) . ' ' . $symbol;
}

/**
 * Get current currency from session or default
 * الحصول على العملة الحالية من الجلسة أو الافتراضية
 */
function get_current_currency() {
    return $_SESSION['currency'] ?? DEFAULT_CURRENCY;
}

/**
 * Set current currency in session
 * تحديد العملة الحالية في الجلسة
 */
function set_current_currency($currency) {
    if (array_key_exists($currency, SUPPORTED_CURRENCIES)) {
        $_SESSION['currency'] = $currency;
        return true;
    }
    return false;
}

/**
 * Generate unique order number
 * إنشاء رقم طلب فريد
 */
function generate_order_number() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

/**
 * Upload image file
 * رفع ملف صورة
 */
function upload_image($file, $upload_dir) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = "{$upload_dir}{$new_filename}";
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return $new_filename;
    }
    
    return false;
}

/**
 * Delete image file
 * حذف ملف صورة
 */
function delete_image($filename, $upload_dir) {
    $file_path = "{$upload_dir}{$filename}";
    if (file_exists($file_path)) {
        return unlink($file_path);
    }
    return false;
}

/**
 * Get cart item count
 * الحصول على عدد عناصر السلة
 */
function get_cart_count() {
    if (!isset($_SESSION['cart'])) {
        return 0;
    }
    return array_sum($_SESSION['cart']);
}

/**
 * Add product to cart
 * إضافة منتج للسلة
 */
function add_to_cart($product_id, $variant_id = null, $quantity = 1) {
    $cart_key = $variant_id ? "{$product_id}_{$variant_id}" : (string)$product_id;
    
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = array();
    }
    
    if (isset($_SESSION['cart'][$cart_key])) {
        $_SESSION['cart'][$cart_key] += $quantity;
    } else {
        $_SESSION['cart'][$cart_key] = $quantity;
    }
    
    return true;
}

/**
 * Remove product from cart
 * إزالة منتج من السلة
 */
function remove_from_cart($product_id, $variant_id = null) {
    $cart_key = $variant_id ? "{$product_id}_{$variant_id}" : (string)$product_id;
    
    if (isset($_SESSION['cart'][$cart_key])) {
        unset($_SESSION['cart'][$cart_key]);
        return true;
    }
    
    return false;
}

/**
 * Clear cart
 * مسح السلة
 */
function clear_cart() {
    unset($_SESSION['cart']);
}

/**
 * Send Telegram notification
 * إرسال إشعار تليجرام
 */
function send_telegram_notification($message) {
    if (empty(TELEGRAM_BOT_TOKEN) || empty(TELEGRAM_CHAT_ID)) {
        return false;
    }
    
    $url = "https://api.telegram.org/bot" . TELEGRAM_BOT_TOKEN . "/sendMessage";
    $data = array(
        'chat_id' => TELEGRAM_CHAT_ID,
        'text' => $message,
        'parse_mode' => 'HTML'
    );
    
    $options = array(
        'http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        )
    );
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return $result !== false;
}

/**
 * Generate slug from Arabic/English text
 * إنشاء slug من النص العربي/الإنجليزي
 */
function generate_slug($text) {
    // Convert to lowercase
    $text = mb_strtolower($text, 'UTF-8');

    // Replace Arabic characters with transliteration
    $arabic_map = [
        'ا' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
        'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'th', 'ر' => 'r',
        'ز' => 'z', 'س' => 's', 'ش' => 'sh', 'ص' => 's', 'ض' => 'd',
        'ط' => 't', 'ظ' => 'z', 'ع' => 'a', 'غ' => 'gh', 'ف' => 'f',
        'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
        'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ى' => 'a', 'ة' => 'h',
        'أ' => 'a', 'إ' => 'i', 'آ' => 'a', 'ؤ' => 'w', 'ئ' => 'y'
    ];

    // Replace Arabic characters
    $text = strtr($text, $arabic_map);

    // Remove special characters and replace spaces with hyphens
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');

    return $text;
}

/**
 * Generate product URL with slug
 * إنشاء رابط المنتج مع slug
 */
function generate_product_url($product_id, $product_name = '') {
    if ($product_name) {
        $slug = generate_slug($product_name);
        return BASE_URL . "product/{$slug}-{$product_id}";
    }
    return BASE_URL . "product/{$product_id}";
}

/**
 * Extract product ID from slug URL
 * استخراج معرف المنتج من رابط slug
 */
function extract_product_id_from_slug($slug) {
    // Extract ID from slug like "product-name-123" -> 123
    if (preg_match('/-(\d+)$/', $slug, $matches)) {
        return (int)$matches[1];
    }

    // If it's just a number, return it
    if (is_numeric($slug)) {
        return (int)$slug;
    }

    return 0;
}

/**
 * Generate pagination HTML
 * إنشاء HTML للترقيم
 */
function generate_pagination($current_page, $total_pages, $base_url) {
    if ($total_pages <= 1) return '';

    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

    // Previous button
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $html .= "<li class=\"page-item\"><a class=\"page-link\" href=\"{$base_url}?page={$prev_page}\">السابق</a></li>";
    }

    // Page numbers
    for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++) {
        $active = ($i == $current_page) ? 'active' : '';
        $html .= "<li class=\"page-item {$active}\"><a class=\"page-link\" href=\"{$base_url}?page={$i}\">{$i}</a></li>";
    }

    // Next button
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $html .= "<li class=\"page-item\"><a class=\"page-link\" href=\"{$base_url}?page={$next_page}\">التالي</a></li>";
    }

    $html .= '</ul></nav>';
    return $html;
}
