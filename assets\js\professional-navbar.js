/**
 * Professional Navbar JavaScript - جافا سكريبت شريط التنقل الاحترافي
 * Enhanced functionality for the professional responsive navbar
 */

document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.professional-navbar');
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.querySelector('.search-suggestions');
    const togglerButton = document.querySelector('.professional-toggler');
    
    // Navbar scroll effects
    let lastScrollTop = 0;
    let scrollTimeout;
    
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add scrolled class for styling
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Auto-hide navbar on scroll down, show on scroll up
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
        
        // Clear loading animation after scroll
        clearTimeout(scrollTimeout);
        navbar.classList.add('navbar-loading');
        scrollTimeout = setTimeout(() => {
            navbar.classList.remove('navbar-loading');
        }, 150);
    }
    
    // Add transition to navbar
    if (navbar) {
        navbar.style.transition = 'transform 0.3s ease, min-height 0.3s ease';
    }
    
    // Throttled scroll event
    let ticking = false;
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(function() {
                handleScroll();
                ticking = false;
            });
            ticking = true;
        }
    });
    
    // Professional search functionality
    let searchTimeout;
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    fetchSearchSuggestions(query);
                }, 300);
            } else {
                hideSuggestions();
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.professional-search')) {
                hideSuggestions();
            }
        });
        
        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            const suggestions = searchSuggestions?.querySelectorAll('.suggestion-item') || [];
            let currentIndex = Array.from(suggestions).findIndex(item => item.classList.contains('active'));
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentIndex = currentIndex < suggestions.length - 1 ? currentIndex + 1 : 0;
                    updateActiveSuggestion(suggestions, currentIndex);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    currentIndex = currentIndex > 0 ? currentIndex - 1 : suggestions.length - 1;
                    updateActiveSuggestion(suggestions, currentIndex);
                    break;
                case 'Enter':
                    if (currentIndex >= 0 && suggestions[currentIndex]) {
                        e.preventDefault();
                        suggestions[currentIndex].click();
                    }
                    break;
                case 'Escape':
                    hideSuggestions();
                    searchInput.blur();
                    break;
            }
        });
    }
    
    function fetchSearchSuggestions(query) {
        // Get base URL from the page
        const baseUrl = document.querySelector('meta[name="base-url"]')?.content || 
                       window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
        
        fetch(`${baseUrl}api/search-suggestions.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.suggestions.length > 0) {
                showSuggestions(data.suggestions);
            } else {
                hideSuggestions();
            }
        })
        .catch(error => {
            console.error('Error fetching suggestions:', error);
            hideSuggestions();
        });
    }
    
    function showSuggestions(suggestions) {
        if (!searchSuggestions) return;
        
        searchSuggestions.innerHTML = suggestions.map(item => `
            <div class="suggestion-item" data-value="${item.name}" data-url="${item.url || ''}">
                <div class="suggestion-icon">
                    <i class="${item.icon || 'fas fa-search'}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-name">${item.name}</div>
                    <div class="suggestion-category">${item.category || 'منتج'}</div>
                </div>
            </div>
        `).join('');
        
        searchSuggestions.style.display = 'block';
        
        // Add click handlers
        searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const url = this.dataset.url;
                if (url) {
                    window.location.href = url;
                } else {
                    searchInput.value = this.dataset.value;
                    hideSuggestions();
                    searchInput.closest('form').submit();
                }
            });
        });
    }
    
    function hideSuggestions() {
        if (searchSuggestions) {
            searchSuggestions.style.display = 'none';
        }
    }
    
    function updateActiveSuggestion(suggestions, index) {
        suggestions.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
    }
    
    // Professional mobile menu toggle
    if (togglerButton) {
        togglerButton.addEventListener('click', function() {
            this.classList.toggle('active');
            
            // Animate hamburger lines
            const spans = this.querySelectorAll('span');
            spans.forEach((span, index) => {
                span.style.transition = 'all 0.3s ease';
                if (this.classList.contains('active')) {
                    switch(index) {
                        case 0:
                            span.style.transform = 'rotate(45deg) translate(5px, 5px)';
                            break;
                        case 1:
                            span.style.opacity = '0';
                            break;
                        case 2:
                            span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
                            break;
                    }
                } else {
                    span.style.transform = 'none';
                    span.style.opacity = '1';
                }
            });
        });
    }
    
    // Dynamic cart count update
    function updateCartCount() {
        const baseUrl = document.querySelector('meta[name="base-url"]')?.content || 
                       window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
        
        fetch(`${baseUrl}api/cart.php?action=get_count`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cartBadge = document.querySelector('.cart-badge');
                const cartIcon = document.querySelector('.cart-icon');
                
                if (data.count > 0) {
                    if (cartBadge) {
                        cartBadge.textContent = data.count;
                        cartBadge.style.animation = 'pulse 0.5s ease';
                    } else if (cartIcon) {
                        // Create badge if it doesn't exist
                        const badge = document.createElement('span');
                        badge.className = 'cart-badge';
                        badge.textContent = data.count;
                        cartIcon.appendChild(badge);
                        
                        // Animate new badge
                        setTimeout(() => {
                            badge.style.animation = 'pulse 0.5s ease';
                        }, 100);
                    }
                } else if (cartBadge) {
                    cartBadge.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => cartBadge.remove(), 300);
                }
            }
        })
        .catch(error => console.error('Error updating cart count:', error));
    }
    
    // Update cart count every 30 seconds
    setInterval(updateCartCount, 30000);
    
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target && navbar) {
                const offsetTop = target.offsetTop - navbar.offsetHeight - 20;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Professional dropdown animations
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            let hoverTimeout;
            
            dropdown.addEventListener('mouseenter', function() {
                if (window.innerWidth > 991) {
                    clearTimeout(hoverTimeout);
                    menu.style.display = 'block';
                    menu.style.opacity = '0';
                    menu.style.transform = menu.classList.contains('mega-menu') ? 
                        'translateX(-50%) translateY(10px)' : 'translateY(-10px)';
                    
                    setTimeout(() => {
                        menu.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        menu.style.opacity = '1';
                        menu.style.transform = menu.classList.contains('mega-menu') ? 
                            'translateX(-50%) translateY(0)' : 'translateY(0)';
                    }, 10);
                }
            });
            
            dropdown.addEventListener('mouseleave', function() {
                if (window.innerWidth > 991) {
                    hoverTimeout = setTimeout(() => {
                        menu.style.opacity = '0';
                        menu.style.transform = menu.classList.contains('mega-menu') ? 
                            'translateX(-50%) translateY(10px)' : 'translateY(-10px)';
                        
                        setTimeout(() => {
                            menu.style.display = 'none';
                        }, 400);
                    }, 100);
                }
            });
        }
    });
    
    // Add loading states for navigation links
    const navLinks = document.querySelectorAll('.professional-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.href && !this.href.includes('#') && !this.href.includes('javascript:')) {
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';
                
                // Add loading spinner
                const spinner = document.createElement('span');
                spinner.className = 'navbar-spinner';
                this.appendChild(spinner);
                
                // Reset after 2 seconds in case of slow loading
                setTimeout(() => {
                    this.style.opacity = '1';
                    this.style.pointerEvents = 'auto';
                    if (spinner.parentNode) {
                        spinner.remove();
                    }
                }, 2000);
            }
        });
    });
    
    // Professional notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `navbar-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    // Expose notification function globally
    window.showNavbarNotification = showNotification;
    
    // Initialize tooltips for better UX
    const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element);
        });
    }
    
    // Performance optimization: Lazy load dropdown content
    const lazyDropdowns = document.querySelectorAll('.dropdown[data-lazy="true"]');
    lazyDropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        let loaded = false;
        
        toggle.addEventListener('mouseenter', function() {
            if (!loaded) {
                // Load dropdown content dynamically
                loadDropdownContent(dropdown);
                loaded = true;
            }
        });
    });
    
    function loadDropdownContent(dropdown) {
        // This can be extended to load content via AJAX
        console.log('Loading dropdown content for:', dropdown);
    }
    
    // Enhanced accessibility features
    document.addEventListener('keydown', function(e) {
        // ESC key to close all dropdowns
        if (e.key === 'Escape') {
            const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
            openDropdowns.forEach(menu => {
                const dropdown = bootstrap.Dropdown.getInstance(menu.previousElementSibling);
                if (dropdown) {
                    dropdown.hide();
                }
            });
        }
        
        // Alt + M to focus on main navigation
        if (e.altKey && e.key === 'm') {
            e.preventDefault();
            const firstNavLink = document.querySelector('.professional-nav .nav-link');
            if (firstNavLink) {
                firstNavLink.focus();
            }
        }
        
        // Alt + S to focus on search
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
    
    // Touch gestures for mobile
    let touchStartX = 0;
    let touchStartY = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        if (!touchStartX || !touchStartY) return;
        
        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;
        
        const diffX = touchStartX - touchEndX;
        const diffY = touchStartY - touchEndY;
        
        // Swipe down to show navbar
        if (Math.abs(diffY) > Math.abs(diffX) && diffY < -50 && window.pageYOffset > 100) {
            if (navbar) {
                navbar.style.transform = 'translateY(0)';
            }
        }
        
        touchStartX = 0;
        touchStartY = 0;
    });
    
    // Initialize navbar on page load
    console.log('Professional Navbar initialized successfully');
});

// Utility functions for external use
window.NavbarUtils = {
    showNotification: function(message, type = 'info') {
        if (window.showNavbarNotification) {
            window.showNavbarNotification(message, type);
        }
    },
    
    updateCartCount: function() {
        // Trigger cart count update
        const event = new CustomEvent('updateCartCount');
        document.dispatchEvent(event);
    },
    
    hideNavbar: function() {
        const navbar = document.querySelector('.professional-navbar');
        if (navbar) {
            navbar.style.transform = 'translateY(-100%)';
        }
    },
    
    showNavbar: function() {
        const navbar = document.querySelector('.professional-navbar');
        if (navbar) {
            navbar.style.transform = 'translateY(0)';
        }
    }
};
