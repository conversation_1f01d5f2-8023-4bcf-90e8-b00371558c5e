<?php
/**
 * General Configuration
 * التكوين العام للموقع
 */

// Site Configuration
define('SITE_NAME', 'Shoppy - متجر إلكتروني');
define('SITE_URL', 'http://localhost/shoppy');
define('ADMIN_EMAIL', '<EMAIL>');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/uploads/');
define('PRODUCT_IMAGES_PATH', ROOT_PATH . '/assets/images/products/');

// URLs - Auto-detect protocol and host
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$base_path = '/shoppy/';

define('BASE_URL', $protocol . $host . $base_path);
define('ADMIN_URL', BASE_URL . 'admin/');
define('ASSETS_URL', BASE_URL . 'assets/');

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Telegram Bot Configuration
define('TELEGRAM_BOT_TOKEN', ''); // Add your bot token here
define('TELEGRAM_CHAT_ID', ''); // Add your chat ID here

// Session Configuration (must be set before session_start)
if (session_status() === PHP_SESSION_NONE) {
    // Session settings
    ini_set('session.cookie_lifetime', 86400); // 24 hours
    ini_set('session.gc_maxlifetime', 86400);

    // Security settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 when using HTTPS

    // Start session
    session_start();
}

// Error Reporting (disable in production)
if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT_PATH . '/logs/error.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Performance Settings
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
ini_set('max_input_vars', 3000);

// Currency Settings
define('DEFAULT_CURRENCY', 'SAR');
define('SUPPORTED_CURRENCIES', [
    'SAR' => ['symbol' => 'ر.س', 'name' => 'ريال سعودي'],
    'EGP' => ['symbol' => 'ج.م', 'name' => 'جنيه مصري'],
    'USD' => ['symbol' => '$', 'name' => 'دولار أمريكي'],
    'EUR' => ['symbol' => '€', 'name' => 'يورو']
]);

// Timezone
date_default_timezone_set('Asia/Riyadh');

// Include database configuration
require_once 'database.php';
