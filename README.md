# متجر شوبي الإلكتروني - Shoppy E-Commerce

متجر إلكتروني متكامل مبني بـ PHP مع دعم كامل للغة العربية ونظام إدارة شامل.

## المميزات الرئيسية

### للعملاء
- 🛍️ تصفح المنتجات بسهولة مع فلترة متقدمة
- 🛒 نظام سلة تسوق متطور
- 👤 حسابات المستخدمين مع لوحة تحكم شخصية
- 📱 تصميم متجاوب يعمل على جميع الأجهزة
- 🔍 بحث ذكي في المنتجات
- 💳 نظام طلبات آمن
- 📦 تتبع حالة الطلبات

### للإدارة
- 📊 لوحة تحكم شاملة مع إحصائيات مفصلة
- 📦 إدارة المنتجات والتصنيفات
- 🛍️ إدارة الطلبات وتحديث الحالات
- 👥 إدارة المستخدمين
- 🏠 تحرير محتوى الصفحة الرئيسية
- 📈 تقارير المبيعات والأداء

## التقنيات المستخدمة

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **Architecture**: MVC Pattern

## متطلبات التشغيل

- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx Web Server
- مساحة تخزين 500MB على الأقل

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/shoppy-ecommerce.git
cd shoppy-ecommerce
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة باسم `shoppy_db`
2. استورد ملف قاعدة البيانات:
```sql
mysql -u username -p shoppy_db < database/shoppy_db.sql
```

### 3. تكوين الإعدادات
1. انسخ ملف الإعدادات:
```bash
cp config/config.example.php config/config.php
```

2. حدث إعدادات قاعدة البيانات في `config/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'shoppy_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات
```bash
chmod 755 assets/images/
chmod 755 assets/images/products/
chmod 755 assets/images/categories/
```

### 5. تشغيل الموقع
- ضع الملفات في مجلد الويب (htdocs/www)
- تأكد من تشغيل Apache/Nginx و MySQL
- افتح المتصفح وانتقل إلى `http://localhost/shoppy`

## الحسابات التجريبية

### حساب المدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

### حساب العميل
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

## هيكل المشروع

```
shoppy/
├── admin/                  # لوحة تحكم الإدارة
│   ├── categories/         # إدارة التصنيفات
│   ├── orders/            # إدارة الطلبات
│   ├── products/          # إدارة المنتجات
│   └── users/             # إدارة المستخدمين
├── api/                   # واجهات برمجة التطبيقات
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات الأنماط
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور
├── auth/                  # نظام المصادقة
├── classes/               # فئات PHP
├── config/                # ملفات التكوين
├── database/              # ملفات قاعدة البيانات
├── includes/              # ملفات مساعدة
└── user/                  # صفحات المستخدم
```

## الاستخدام

### إضافة منتج جديد
1. سجل دخول كمدير
2. انتقل إلى "إدارة المنتجات"
3. اضغط "إضافة منتج جديد"
4. املأ البيانات المطلوبة
5. ارفع صور المنتج
6. احفظ المنتج

### إدارة الطلبات
1. انتقل إلى "إدارة الطلبات"
2. اعرض تفاصيل الطلب
3. حدث حالة الطلب حسب التقدم
4. تواصل مع العميل عند الحاجة

### تخصيص الصفحة الرئيسية
1. انتقل إلى "إدارة الصفحة الرئيسية"
2. حرر الأقسام المختلفة
3. أضف أو احذف المحتوى
4. احفظ التغييرات

## الأمان

- 🔒 تشفير كلمات المرور باستخدام bcrypt
- 🛡️ حماية من هجمات SQL Injection
- 🔐 حماية من هجمات XSS
- 🚫 حماية من هجمات CSRF
- 👮 نظام صلاحيات متقدم

## الأداء

- ⚡ استعلامات قاعدة بيانات محسنة
- 🗂️ فهرسة ذكية للجداول
- 📱 تحميل تدريجي للصور
- 💾 تخزين مؤقت للبيانات
- 🔄 ضغط الملفات

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

- 📧 البريد الإلكتروني: <EMAIL>
- 💬 التليجرام: @shoppy_support
- 🐛 تقرير الأخطاء: [GitHub Issues](https://github.com/your-username/shoppy-ecommerce/issues)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التحديثات المستقبلية

- [ ] نظام دفع إلكتروني متكامل
- [ ] تطبيق جوال
- [ ] نظام نقاط الولاء
- [ ] دعم متعدد اللغات
- [ ] نظام تقييم المنتجات
- [ ] نظام كوبونات الخصم
- [ ] تكامل مع وسائل التواصل الاجتماعي

## الشكر والتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا المشروع.

---

**تم تطوير المشروع بـ ❤️ في المملكة العربية السعودية**
