<?php
/**
 * Shopping Cart Page - صفحة عربة التسوق
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/image_helper.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_cart'])) {
        // Update cart quantities
        foreach ($_POST['quantities'] as $cart_key => $quantity) {
            $quantity = (int)$quantity;
            if ($quantity <= 0) {
                unset($_SESSION['cart'][$cart_key]);
            } else {
                $_SESSION['cart'][$cart_key] = $quantity;
            }
        }
        $success_message = "تم تحديث السلة بنجاح!";
    }
    
    if (isset($_POST['remove_item'])) {
        $cart_key = $_POST['cart_key'];
        unset($_SESSION['cart'][$cart_key]);
        $success_message = "تم حذف المنتج من السلة!";
    }
    
    if (isset($_POST['clear_cart'])) {
        clear_cart();
        $success_message = "تم مسح السلة بالكامل!";
    }
}

// Get cart items with product details
$cart_items = array();
$cart_total = 0;
$shipping_cost = 25.00; // Default shipping cost
$tax_rate = 0.15; // 15% tax

if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $cart_key => $quantity) {
        $parts = explode('_', $cart_key);
        $product_id = (int)$parts[0];
        $variant_id = isset($parts[1]) ? (int)$parts[1] : null;
        
        $product = $product_class->getProductById($product_id);
        if ($product) {
            $item_price = $product['sale_price'] ?: $product['price'];
            $item_total = $item_price * $quantity;
            
            // Get primary image
            $images = $product_class->getProductImages($product_id);
            $primary_image = !empty($images) ? $images[0]['image_path'] : null;
            
            // Get variant details if applicable
            $variant_info = '';
            if ($variant_id) {
                $variants = $product_class->getProductVariants($product_id);
                foreach ($variants as $variant) {
                    if ($variant['id'] == $variant_id) {
                        $variant_info = $variant['attributes'];
                        $item_price = $variant['price'] ?: $item_price;
                        $item_total = $item_price * $quantity;
                        break;
                    }
                }
            }
            
            $cart_items[] = array(
                'cart_key' => $cart_key,
                'product_id' => $product_id,
                'variant_id' => $variant_id,
                'name' => $product['name'],
                'price' => $item_price,
                'quantity' => $quantity,
                'total' => $item_total,
                'image' => $primary_image,
                'variant_info' => $variant_info,
                'stock_quantity' => $product['stock_quantity']
            );
            
            $cart_total += $item_total;
        }
    }
}

$subtotal = $cart_total;
$tax_amount = $cart_total * $tax_rate;
$final_total = $cart_total + $shipping_cost + $tax_amount;

$page_title = "عربة التسوق - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .cart-item {
            border-bottom: 1px solid #dee2e6;
            padding: 1.5rem 0;
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .cart-item-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .quantity-input {
            width: 80px;
        }
        
        .cart-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            position: sticky;
            top: 100px;
        }
        
        .empty-cart {
            text-align: center;
            padding: 4rem 0;
        }
        
        .btn-remove {
            color: #dc3545;
            background: none;
            border: none;
            font-size: 1.2rem;
        }
        
        .btn-remove:hover {
            color: #c82333;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">عربة التسوق</li>
            </ol>
        </nav>
    </div>

    <!-- Success Message -->
    <?php if (isset($success_message)): ?>
        <div class="container">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h2 mb-4">
                    <i class="fas fa-shopping-cart me-2"></i>
                    عربة التسوق
                    <?php if (!empty($cart_items)): ?>
                        <span class="badge bg-primary"><?php echo count($cart_items); ?> منتج</span>
                    <?php endif; ?>
                </h1>
            </div>
        </div>

        <?php if (empty($cart_items)): ?>
            <!-- Empty Cart -->
            <div class="empty-cart">
                <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                <h3>عربة التسوق فارغة</h3>
                <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى عربة التسوق بعد</p>
                <a href="<?php echo BASE_URL; ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>
                    ابدأ التسوق الآن
                </a>
            </div>
        <?php else: ?>
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">المنتجات في السلة</h5>
                            <form method="POST" class="d-inline">
                                <button type="submit" name="clear_cart" class="btn btn-outline-danger btn-sm"
                                        onclick="return confirm('هل أنت متأكد من مسح جميع المنتجات؟')">
                                    <i class="fas fa-trash me-1"></i>
                                    مسح السلة
                                </button>
                            </form>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="cartForm">
                                <?php foreach ($cart_items as $item): ?>
                                    <div class="cart-item">
                                        <div class="row align-items-center">
                                            <!-- Product Image -->
                                            <div class="col-md-2 col-3">
                                                <?php if ($item['image']): ?>
                                                    <img src="<?php echo ASSETS_URL; ?>images/products/<?php echo $item['image']; ?>" 
                                                         class="cart-item-image" alt="<?php echo $item['name']; ?>">
                                                <?php else: ?>
                                                    <div class="cart-item-image bg-light d-flex align-items-center justify-content-center">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <!-- Product Info -->
                                            <div class="col-md-4 col-9">
                                                <h6 class="mb-1">
                                                    <a href="product.php?id=<?php echo $item['product_id']; ?>" 
                                                       class="text-decoration-none">
                                                        <?php echo $item['name']; ?>
                                                    </a>
                                                </h6>
                                                <?php if ($item['variant_info']): ?>
                                                    <small class="text-muted"><?php echo $item['variant_info']; ?></small>
                                                <?php endif; ?>
                                                <div class="text-primary fw-bold">
                                                    <?php echo format_price($item['price']); ?>
                                                </div>
                                            </div>
                                            
                                            <!-- Quantity -->
                                            <div class="col-md-2 col-4">
                                                <label class="form-label small">الكمية</label>
                                                <input type="number" 
                                                       name="quantities[<?php echo $item['cart_key']; ?>]" 
                                                       value="<?php echo $item['quantity']; ?>" 
                                                       min="1" 
                                                       max="<?php echo $item['stock_quantity']; ?>"
                                                       class="form-control quantity-input"
                                                       onchange="updateCartTotals()">
                                            </div>
                                            
                                            <!-- Total -->
                                            <div class="col-md-2 col-4">
                                                <label class="form-label small">الإجمالي</label>
                                                <div class="fw-bold text-success">
                                                    <?php echo format_price($item['total']); ?>
                                                </div>
                                            </div>
                                            
                                            <!-- Remove Button -->
                                            <div class="col-md-2 col-4 text-end">
                                                <button type="submit" name="remove_item" value="<?php echo $item['cart_key']; ?>" 
                                                        class="btn-remove" title="حذف المنتج"
                                                        onclick="return confirm('هل تريد حذف هذا المنتج؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <input type="hidden" name="cart_key" value="<?php echo $item['cart_key']; ?>">
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                
                                <div class="text-end mt-3">
                                    <button type="submit" name="update_cart" class="btn btn-outline-primary">
                                        <i class="fas fa-sync me-1"></i>
                                        تحديث السلة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <div class="cart-summary">
                        <h5 class="mb-4">ملخص الطلب</h5>
                        
                        <?php $current_currency = get_current_currency(); ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span><?php echo format_price($subtotal, $current_currency); ?></span>
                        </div>

                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span><?php echo format_price($shipping_cost, $current_currency); ?></span>
                        </div>

                        <div class="d-flex justify-content-between mb-3">
                            <span>الضريبة (15%):</span>
                            <span><?php echo format_price($tax_amount, $current_currency); ?></span>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between mb-4">
                            <strong>الإجمالي النهائي:</strong>
                            <strong class="text-primary h5"><?php echo format_price($final_total, $current_currency); ?></strong>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="checkout.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card me-2"></i>
                                إتمام الطلب
                            </a>
                            
                            <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                متابعة التسوق
                            </a>
                        </div>
                        
                        <!-- Shipping Info -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="mb-2">
                                <i class="fas fa-truck text-primary me-2"></i>
                                معلومات الشحن
                            </h6>
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-1"></i> شحن مجاني للطلبات أكثر من 200 ريال</li>
                                <li><i class="fas fa-check text-success me-1"></i> التوصيل خلال 2-3 أيام عمل</li>
                                <li><i class="fas fa-check text-success me-1"></i> إمكانية الإرجاع خلال 14 يوم</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function updateCartTotals() {
            // This function can be enhanced to update totals dynamically via AJAX
            // For now, we'll just submit the form when quantities change
            setTimeout(function() {
                document.getElementById('cartForm').submit();
            }, 1000);
        }
        
        // Auto-submit form when quantity changes (with debounce)
        let timeoutId;
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(function() {
                    document.getElementById('cartForm').submit();
                }, 2000); // Wait 2 seconds after user stops typing
            });
        });
    </script>
</body>
</html>
