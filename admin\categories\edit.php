<?php
/**
 * Edit Category Page - صفحة تعديل التصنيف
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Category.php';

// Check admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$category = new Category($db);

// Get category ID
$category_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$category_id) {
    header('Location: index.php');
    exit();
}

// Get category details
$category_data = $category->getCategoryById($category_id);
if (!$category_data) {
    header('Location: index.php');
    exit();
}

// Get all categories for parent dropdown
$all_categories = $category->getAllCategories(false);

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_category'])) {
        $name = trim($_POST['name']);
        $description = trim($_POST['description']);
        $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
        $sort_order = (int)$_POST['sort_order'];
        $image = '';
        
        // Handle image upload
        if (!empty($_FILES['image']['name'])) {
            $upload_dir = UPLOAD_PATH . 'categories/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $uploaded_filename = upload_image($_FILES['image'], $upload_dir);
            if ($uploaded_filename) {
                $image = $uploaded_filename;
                // Delete old image if exists
                if ($category_data['image']) {
                    delete_image($category_data['image'], $upload_dir);
                }
            }
        } else {
            $image = $category_data['image'];
        }
        
        // Validation
        if (empty($name)) {
            $error_message = "يرجى إدخال اسم التصنيف";
        } else {
            $data = [
                'name' => $name,
                'description' => $description,
                'image' => $image,
                'parent_id' => $parent_id,
                'sort_order' => $sort_order
            ];
            
            if ($category->updateCategory($category_id, $data)) {
                $success_message = "تم تحديث التصنيف بنجاح!";
                // Refresh category data
                $category_data = $category->getCategoryById($category_id);
            } else {
                $error_message = "حدث خطأ أثناء تحديث التصنيف";
            }
        }
    }
}

$page_title = "تعديل التصنيف: " . $category_data['name'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    
    <style>
        .form-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .image-upload-area:hover {
            border-color: #0d6efd;
            background: #e7f3ff;
        }
        
        .current-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-shopping-bag ms-2 ms-1"></i>لوحة تحكم <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../../auth/logout.php">
                    <i class="fas fa-sign-out-alt ms-1"></i>تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2">
                <div class="list-group">
                    <a href="../index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt ms-2 ms-1"></i>الرئيسية
                    </a>
                    <a href="../products/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-box ms-2 ms-1"></i>المنتجات
                    </a>
                    <a href="index.php" class="list-group-item list-group-item-action active">
                        <i class="fas fa-tags ms-2 ms-1"></i>التصنيفات
                    </a>
                    <a href="../orders/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart ms-2 ms-1"></i>الطلبات
                    </a>
                    <a href="../settings.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-cog ms-2 ms-1"></i>الإعدادات
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-edit ms-2 ms-1"></i>تعديل التصنيف: <?php echo $category_data['name']; ?>
                    </h2>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right ms-2 ms-1"></i>العودة للتصنيفات
                    </a>
                </div>

                <!-- Messages -->
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle ms-2 ms-1"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle ms-2 ms-1"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Edit Category Form -->
                <form method="POST" enctype="multipart/form-data">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle ms-2 ms-1"></i>معلومات التصنيف
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم التصنيف *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($category_data['name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">التصنيف الأب</label>
                                    <select class="form-select" id="parent_id" name="parent_id">
                                        <option value="">تصنيف رئيسي</option>
                                        <?php foreach ($all_categories as $cat): ?>
                                            <?php if ($cat['id'] != $category_id): // Don't allow self as parent ?>
                                                <option value="<?php echo $cat['id']; ?>" 
                                                        <?php echo $cat['id'] == $category_data['parent_id'] ? 'selected' : ''; ?>>
                                                    <?php echo $cat['name']; ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ترتيب العرض</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="<?php echo $category_data['sort_order']; ?>" min="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف التصنيف</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4"><?php echo htmlspecialchars($category_data['description']); ?></textarea>
                        </div>
                    </div>

                    <!-- Current Image -->
                    <?php if ($category_data['image']): ?>
                        <div class="form-section">
                            <h5 class="mb-4">
                                <i class="fas fa-image ms-2 ms-1"></i>الصورة الحالية
                            </h5>
                            
                            <div class="text-center mb-3">
                                <img src="<?php echo BASE_URL; ?>uploads/categories/<?php echo $category_data['image']; ?>" 
                                     class="current-image" alt="<?php echo $category_data['name']; ?>">
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Image Upload -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-upload ms-2 ms-1"></i> <?php echo $category_data['image'] ? 'تغيير الصورة' : 'إضافة صورة'; ?>
                        </h5>
                        
                        <div class="image-upload-area" onclick="document.getElementById('image').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3 ms-1"></i> <h6>اضغط لرفع صورة أو اسحبها هنا</h6>
                            <p class="text-muted mb-0">يُفضل أن تكون الصورة بأبعاد مربعة (JPG, PNG, GIF, WEBP)</p>
                            <input type="file" id="image" name="image" accept="image/*" class="d-none">
                        </div>
                        
                        <div class="preview-container mt-3" id="imagePreview"></div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-section">
                        <div class="d-flex gap-3">
                            <button type="submit" name="update_category" class="btn btn-primary btn-lg">
                                <i class="fas fa-save ms-2 ms-1"></i>حفظ التغييرات
                            </button>
                            <a href="index.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times ms-2 ms-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Image upload handling
        const imageInput = document.getElementById('image');
        const imagePreview = document.getElementById('imagePreview');
        const uploadArea = document.querySelector('.image-upload-area');
        
        imageInput.addEventListener('change', handleImageSelect);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                imageInput.files = files;
                handleImageSelect();
            }
        });
        
        function handleImageSelect() {
            const file = imageInput.files[0];
            imagePreview.innerHTML = '';
            
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'text-center';
                    previewItem.innerHTML = `
                        <img src="${e.target.result}" class="current-image" alt="Preview">
                        <p class="mt-2 text-muted">معاينة الصورة الجديدة</p>
                    `;
                    imagePreview.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</body>
</html>
