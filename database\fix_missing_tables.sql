-- Fix Missing Tables - إصلاح الجداول المفقودة
-- Run this SQL to fix the missing product_variant_attributes table

USE shoppy_ecommerce;

-- Create product_variant_attributes table if it doesn't exist
CREATE TABLE IF NOT EXISTS product_variant_attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    variant_id INT NOT NULL,
    attribute_value_id INT NOT NULL,
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_value_id) REFERENCES product_attribute_values(id) ON DELETE CASCADE,
    UNIQUE KEY unique_variant_attribute (variant_id, attribute_value_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add some sample product attributes if they don't exist
INSERT IGNORE INTO product_attributes (name, display_name) VALUES 
('size', 'المقاس'),
('color', 'اللون'),
('material', 'المادة');

-- Add is_active column to products table if it doesn't exist
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_active TINYINT(1) DEFAULT 1;

-- Update all existing products to be active
UPDATE products SET is_active = 1 WHERE is_active IS NULL;

-- Add some sample attribute values
INSERT IGNORE INTO product_attribute_values (attribute_id, value, display_value, color_code) VALUES
(1, 'small', 'صغير', NULL),
(1, 'medium', 'متوسط', NULL),
(1, 'large', 'كبير', NULL),
(1, 'xl', 'كبير جداً', NULL),
(2, 'red', 'أحمر', '#FF0000'),
(2, 'blue', 'أزرق', '#0000FF'),
(2, 'green', 'أخضر', '#00FF00'),
(2, 'black', 'أسود', '#000000'),
(2, 'white', 'أبيض', '#FFFFFF'),
(3, 'cotton', 'قطن', NULL),
(3, 'polyester', 'بوليستر', NULL),
(3, 'leather', 'جلد', NULL);
