<?php
/**
 * Test Session Fix - اختبار إصلاح الجلسة
 */

// Turn on error reporting to see if warnings still appear
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار إصلاح الجلسة</h1>";
echo "<p>إذا لم تظهر أي تحذيرات أدناه، فقد تم إصلاح المشكلة بنجاح.</p>";
echo "<hr>";

// Include config file (this should not show warnings now)
require_once 'config/config.php';

echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "✅ <strong>تم تحميل ملف التكوين بنجاح!</strong><br>";
echo "حالة الجلسة: " . (session_status() === PHP_SESSION_ACTIVE ? 'نشطة' : 'غير نشطة') . "<br>";
echo "معرف الجلسة: " . session_id() . "<br>";
echo "اسم الموقع: " . SITE_NAME . "<br>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>معلومات الجلسة:</h3>";
echo "<ul>";
echo "<li>session.cookie_lifetime: " . ini_get('session.cookie_lifetime') . " ثانية</li>";
echo "<li>session.gc_maxlifetime: " . ini_get('session.gc_maxlifetime') . " ثانية</li>";
echo "<li>session.cookie_httponly: " . (ini_get('session.cookie_httponly') ? 'مفعل' : 'معطل') . "</li>";
echo "<li>session.use_only_cookies: " . (ini_get('session.use_only_cookies') ? 'مفعل' : 'معطل') . "</li>";
echo "<li>session.cookie_secure: " . (ini_get('session.cookie_secure') ? 'مفعل' : 'معطل') . "</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>الإصلاحات المطبقة:</h3>";
echo "<ul>";
echo "<li>✅ تم التحقق من حالة الجلسة قبل تعديل الإعدادات</li>";
echo "<li>✅ تم دمج جميع إعدادات الجلسة في مكان واحد</li>";
echo "<li>✅ تم ترتيب الكود لتجنب التحذيرات</li>";
echo "<li>✅ تم إزالة التكرار في إعدادات الأمان</li>";
echo "</ul>";
echo "</div>";

echo "<p><a href='index.php' class='btn' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للموقع</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    direction: rtl;
    text-align: right;
}

h1 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.btn:hover {
    background: #0056b3 !important;
}
</style>
