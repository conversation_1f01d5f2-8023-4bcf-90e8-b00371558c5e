<?php
/**
 * Admin Users Management - إدارة المستخدمين
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/User.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize user class
$user_class = new User($db);

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$role_filter = isset($_GET['role']) ? sanitize_input($_GET['role']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['toggle_status'])) {
        $user_id = (int)$_POST['user_id'];
        $new_status = (int)$_POST['new_status'];
        
        if ($user_class->updateUserStatus($user_id, $new_status)) {
            $success_message = $new_status ? "تم تفعيل المستخدم بنجاح!" : "تم إلغاء تفعيل المستخدم بنجاح!";
        } else {
            $error_message = "حدث خطأ أثناء تحديث حالة المستخدم!";
        }
    } elseif (isset($_POST['update_role'])) {
        $user_id = (int)$_POST['user_id'];
        $new_role = sanitize_input($_POST['new_role']);
        
        if ($user_class->updateUserRole($user_id, $new_role)) {
            $success_message = "تم تحديث دور المستخدم بنجاح!";
        } else {
            $error_message = "حدث خطأ أثناء تحديث دور المستخدم!";
        }
    } elseif (isset($_POST['bulk_action'])) {
        $action = $_POST['bulk_action'];
        $selected_users = $_POST['selected_users'] ?? [];
        
        if (!empty($selected_users)) {
            $success_count = 0;
            foreach ($selected_users as $user_id) {
                $user_id = (int)$user_id;
                if ($action === 'activate') {
                    if ($user_class->updateUserStatus($user_id, 1)) {
                        $success_count++;
                    }
                } elseif ($action === 'deactivate') {
                    if ($user_class->updateUserStatus($user_id, 0)) {
                        $success_count++;
                    }
                } elseif ($action === 'make_customer') {
                    if ($user_class->updateUserRole($user_id, 'customer')) {
                        $success_count++;
                    }
                }
            }
            $success_message = "تم تنفيذ العملية على {$success_count} مستخدم بنجاح!";
        } else {
            $error_message = "يرجى اختيار مستخدم واحد على الأقل!";
        }
    }
}

// Build search query
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(username LIKE :search OR email LIKE :search OR first_name LIKE :search OR last_name LIKE :search)";
    $params[':search'] = "%{$search}%";
}

if ($role_filter) {
    $where_conditions[] = "role = :role";
    $params[':role'] = $role_filter;
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = :status";
    $params[':status'] = (int)$status_filter;
}

// Get users with filters
$users = $user_class->getUsersWithFilters($page, 20, $where_conditions, $params);
$total_users = $user_class->getTotalUsersWithFilters($where_conditions, $params);
$total_pages = ceil($total_users / 20);

// Get user statistics
$user_stats = [
    'total' => $user_class->getTotalCount(),
    'active' => $user_class->getActiveUsersCount(),
    'customers' => $user_class->getUsersByRole('customer'),
    'admins' => $user_class->getUsersByRole('admin'),
    'new_today' => $user_class->getNewUsersToday()
];

$page_title = "إدارة المستخدمين - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .user-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .user-card:hover {
            transform: translateY(-2px);
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .user-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .role-admin { background: #d1ecf1; color: #0c5460; }
        .role-customer { background: #fff3cd; color: #856404; }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 1rem;
        }
        
        .stats-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-card h4 {
            margin-bottom: 0.25rem;
            font-weight: bold;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            margin: 0 0.125rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                    <h4 class="mb-0">إدارة المستخدمين</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync ms-2"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle ms-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- User Statistics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card">
                    <i class="fas fa-users text-primary"></i>
                    <h4><?php echo number_format($user_stats['total']); ?></h4>
                    <p class="mb-0 text-muted">إجمالي المستخدمين</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <i class="fas fa-user-check text-success"></i>
                    <h4><?php echo number_format($user_stats['active']); ?></h4>
                    <p class="mb-0 text-muted">نشط</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <i class="fas fa-shopping-cart text-warning"></i>
                    <h4><?php echo number_format($user_stats['customers']); ?></h4>
                    <p class="mb-0 text-muted">عملاء</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <i class="fas fa-user-shield text-info"></i>
                    <h4><?php echo number_format($user_stats['admins']); ?></h4>
                    <p class="mb-0 text-muted">مدراء</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <i class="fas fa-user-plus text-success"></i>
                    <h4><?php echo number_format($user_stats['new_today']); ?></h4>
                    <p class="mb-0 text-muted">جديد اليوم</p>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3 mb-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="البحث في المستخدمين...">
                </div>

                <div class="col-md-2">
                    <label for="role" class="form-label">الدور</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">جميع الأدوار</option>
                        <option value="admin" <?php echo $role_filter == 'admin' ? 'selected' : ''; ?>>مدير</option>
                        <option value="customer" <?php echo $role_filter == 'customer' ? 'selected' : ''; ?>>عميل</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                        <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search ms-2"></i>
                            بحث
                        </button>
                    </div>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times ms-2"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </form>

            <!-- Bulk Actions -->
            <div class="row">
                <div class="col-md-6">
                    <form method="POST" id="bulkActionForm" class="d-flex align-items-center">
                        <select name="bulk_action" class="form-select me-2" style="width: auto;">
                            <option value="">اختر إجراء...</option>
                            <option value="activate">تفعيل المحدد</option>
                            <option value="deactivate">إلغاء تفعيل المحدد</option>
                            <option value="make_customer">تحويل لعميل</option>
                        </select>
                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                            <i class="fas fa-cogs ms-2"></i>
                            تنفيذ
                        </button>
                    </form>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                            <i class="fas fa-check-square ms-1"></i>
                            تحديد الكل
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                            <i class="fas fa-square ms-1"></i>
                            إلغاء التحديد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <?php if (!empty($users)): ?>
            <div class="row">
                <?php foreach ($users as $user): ?>
                    <div class="col-lg-6 col-xl-4 mb-3">
                        <div class="user-card card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <input type="checkbox" name="selected_users[]" value="<?php echo $user['id']; ?>"
                                               class="form-check-input mb-2" style="transform: scale(1.2);">
                                        <div class="user-avatar">
                                            <?php echo strtoupper(substr($user['first_name'] ?: $user['username'], 0, 1)); ?>
                                        </div>
                                    </div>

                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                                            <div>
                                                <span class="user-status <?php echo $user['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                    <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <p class="text-muted small mb-1">@<?php echo htmlspecialchars($user['username']); ?></p>
                                        <p class="text-muted small mb-2"><?php echo htmlspecialchars($user['email']); ?></p>

                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="role-badge <?php echo 'role-' . $user['role']; ?>">
                                                <?php echo $user['role'] == 'admin' ? 'مدير' : 'عميل'; ?>
                                            </span>
                                            <small class="text-muted">
                                                <?php echo date('Y/m/d', strtotime($user['created_at'])); ?>
                                            </small>
                                        </div>

                                        <div class="d-flex gap-1">
                                            <button type="button" class="btn btn-outline-primary btn-action"
                                                    onclick="toggleStatus(<?php echo $user['id']; ?>, <?php echo $user['is_active']; ?>)"
                                                    title="<?php echo $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                <i class="fas fa-<?php echo $user['is_active'] ? 'user-slash' : 'user-check'; ?>"></i>
                                            </button>

                                            <button type="button" class="btn btn-outline-warning btn-action"
                                                    onclick="changeRole(<?php echo $user['id']; ?>, '<?php echo $user['role']; ?>')"
                                                    title="تغيير الدور">
                                                <i class="fas fa-user-cog"></i>
                                            </button>

                                            <button type="button" class="btn btn-outline-info btn-action"
                                                    onclick="viewUserDetails(<?php echo $user['id']; ?>)"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php
                $base_url = 'index.php?';
                if ($search) $base_url .= 'search=' . urlencode($search) . '&';
                if ($role_filter) $base_url .= 'role=' . $role_filter . '&';
                if ($status_filter !== '') $base_url .= 'status=' . $status_filter . '&';
                echo generate_pagination($page, $total_pages, rtrim($base_url, '&'));
                ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>لا توجد مستخدمين</h5>
                <p class="text-muted">لم يتم العثور على مستخدمين مطابقين للبحث</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Bulk actions functions
        function selectAll() {
            const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
            checkboxes.forEach(checkbox => checkbox.checked = true);
        }

        function deselectAll() {
            const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }

        function confirmBulkAction() {
            const action = document.querySelector('select[name="bulk_action"]').value;
            const selectedUsers = document.querySelectorAll('input[name="selected_users[]"]:checked');

            if (!action) {
                alert('يرجى اختيار إجراء أولاً');
                return false;
            }

            if (selectedUsers.length === 0) {
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return false;
            }

            let message = '';
            switch(action) {
                case 'activate':
                    message = `هل أنت متأكد من تفعيل ${selectedUsers.length} مستخدم؟`;
                    break;
                case 'deactivate':
                    message = `هل أنت متأكد من إلغاء تفعيل ${selectedUsers.length} مستخدم؟`;
                    break;
                case 'make_customer':
                    message = `هل أنت متأكد من تحويل ${selectedUsers.length} مستخدم إلى عميل؟`;
                    break;
            }

            return confirm(message);
        }

        // Add selected users to bulk form
        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const selectedUsers = document.querySelectorAll('input[name="selected_users[]"]:checked');

            // Remove existing hidden inputs
            const existingInputs = this.querySelectorAll('input[name="selected_users[]"]');
            existingInputs.forEach(input => input.remove());

            // Add selected users as hidden inputs
            selectedUsers.forEach(checkbox => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'selected_users[]';
                hiddenInput.value = checkbox.value;
                this.appendChild(hiddenInput);
            });
        });

        // Toggle user status
        function toggleStatus(userId, currentStatus) {
            const newStatus = currentStatus ? 0 : 1;
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="toggle_status" value="1">
                <input type="hidden" name="user_id" value="${userId}">
                <input type="hidden" name="new_status" value="${newStatus}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // Change user role
        function changeRole(userId, currentRole) {
            const newRole = currentRole === 'admin' ? 'customer' : 'admin';
            const roleText = newRole === 'admin' ? 'مدير' : 'عميل';

            if (confirm(`هل أنت متأكد من تغيير دور المستخدم إلى "${roleText}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="update_role" value="1">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="new_role" value="${newRole}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // View user details (placeholder)
        function viewUserDetails(userId) {
            alert('عرض تفاصيل المستخدم - سيتم تطوير هذه الميزة قريباً');
        }
    </script>
</body>
</html>
