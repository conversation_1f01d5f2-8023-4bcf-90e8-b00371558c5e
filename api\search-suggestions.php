<?php
/**
 * Search Suggestions API - واجهة برمجة التطبيقات لاقتراحات البحث
 * Returns search suggestions for the professional navbar
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once '../config/config.php';
require_once '../config/database.php';

// Initialize response
$response = [
    'success' => false,
    'suggestions' => [],
    'message' => ''
];

try {
    // Get search query
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    
    if (empty($query)) {
        $response['message'] = 'Search query is required';
        echo json_encode($response);
        exit;
    }
    
    if (strlen($query) < 2) {
        $response['message'] = 'Query too short';
        echo json_encode($response);
        exit;
    }
    
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('Database connection failed');
    }
    
    // Prepare search query with LIMIT for performance
    $searchQuery = "
        SELECT DISTINCT
            p.name,
            p.slug,
            c.name as category,
            p.price,
            p.image,
            'product' as type
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' 
        AND (
            p.name LIKE :query1 
            OR p.description LIKE :query2
            OR c.name LIKE :query3
        )
        ORDER BY 
            CASE 
                WHEN p.name LIKE :exact_query THEN 1
                WHEN p.name LIKE :start_query THEN 2
                ELSE 3
            END,
            p.name ASC
        LIMIT 8
    ";
    
    $stmt = $db->prepare($searchQuery);
    
    // Bind parameters
    $likeQuery = '%' . $query . '%';
    $exactQuery = $query . '%';
    
    $stmt->bindParam(':query1', $likeQuery);
    $stmt->bindParam(':query2', $likeQuery);
    $stmt->bindParam(':query3', $likeQuery);
    $stmt->bindParam(':exact_query', $exactQuery);
    $stmt->bindParam(':start_query', $exactQuery);
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Also search in categories
    $categoryQuery = "
        SELECT DISTINCT
            name,
            slug,
            name as category,
            NULL as price,
            NULL as image,
            'category' as type
        FROM categories
        WHERE status = 'active' 
        AND name LIKE :query
        ORDER BY name ASC
        LIMIT 3
    ";
    
    $categoryStmt = $db->prepare($categoryQuery);
    $categoryStmt->bindParam(':query', $likeQuery);
    $categoryStmt->execute();
    $categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Combine and format results
    $suggestions = [];
    
    // Add categories first
    foreach ($categories as $category) {
        $suggestions[] = [
            'name' => $category['name'],
            'category' => 'تصنيف',
            'type' => 'category',
            'url' => BASE_URL . '?category=' . $category['slug'],
            'icon' => 'fas fa-folder'
        ];
    }
    
    // Add products
    foreach ($products as $product) {
        $suggestions[] = [
            'name' => $product['name'],
            'category' => $product['category'] ?: 'منتج',
            'type' => 'product',
            'price' => $product['price'],
            'image' => $product['image'],
            'url' => BASE_URL . 'product.php?slug=' . $product['slug'],
            'icon' => 'fas fa-box'
        ];
    }
    
    // Add popular searches if no results
    if (empty($suggestions)) {
        $popularQuery = "
            SELECT DISTINCT
                p.name,
                p.slug,
                c.name as category,
                p.price,
                p.image,
                'product' as type
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'active' 
            AND p.featured = 1
            ORDER BY p.views DESC, p.created_at DESC
            LIMIT 5
        ";
        
        $popularStmt = $db->prepare($popularQuery);
        $popularStmt->execute();
        $popularProducts = $popularStmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($popularProducts as $product) {
            $suggestions[] = [
                'name' => $product['name'],
                'category' => 'منتج مميز',
                'type' => 'popular',
                'price' => $product['price'],
                'image' => $product['image'],
                'url' => BASE_URL . 'product.php?slug=' . $product['slug'],
                'icon' => 'fas fa-star'
            ];
        }
    }
    
    $response['success'] = true;
    $response['suggestions'] = $suggestions;
    $response['message'] = count($suggestions) . ' suggestions found';
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    
    // Log error for debugging
    error_log('Search Suggestions API Error: ' . $e->getMessage());
}

// Return JSON response
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
