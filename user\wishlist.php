<?php
/**
 * User Wishlist Page - صفحة المفضلة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/image_helper.php';
require_once '../classes/Product.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: ../auth/login.php');
    exit;
}

$page_title = 'المفضلة - ' . SITE_NAME;

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

// Handle wishlist actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $product_id = (int)($_POST['product_id'] ?? 0);

    if ($action === 'remove' && $product_id > 0) {
        // Remove from session wishlist
        if (isset($_SESSION['wishlist'])) {
            $key = array_search($product_id, $_SESSION['wishlist']);
            if ($key !== false) {
                unset($_SESSION['wishlist'][$key]);
                $_SESSION['wishlist'] = array_values($_SESSION['wishlist']);
                $success_message = 'تم إزالة المنتج من المفضلة';
            }
        }
    }
}

// Get wishlist items from session
$wishlist_items = [];
if (isset($_SESSION['wishlist']) && !empty($_SESSION['wishlist'])) {
    foreach ($_SESSION['wishlist'] as $product_id) {
        $product = $product_class->getProductById($product_id);
        if ($product && $product['is_active']) {
            $wishlist_items[] = $product;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Swiper CSS -->
    <link href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    <link href="<?php echo ASSETS_URL; ?>css/wishlist-enhancements.css" rel="stylesheet">

</head>
<body>
    <?php include '../includes/navbar.php'; ?>
    
    <!-- Page Header -->
    <section class="bg-gradient-primary text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-heart ms-2"></i>
                        المفضلة
                    </h1>
                    <p class="mb-0 opacity-75">منتجاتك المفضلة</p>
                </div>
                <div class="col-auto">
                    <span class="badge bg-light text-dark fs-6">
                        <?php echo count($wishlist_items); ?> منتج
                    </span>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Wishlist Content -->
    <section class="py-5">
        <div class="container">
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle ms-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($wishlist_items)): ?>
                <div class="row">
                    <?php foreach ($wishlist_items as $item): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card product-card wishlist-item h-100 animate-fade-in">
                                <!-- Enhanced Image Container -->
                                <div class="product-image-container">
                                    <?php echo generate_product_image_html(
                                        $item['primary_image'],
                                        $item['name'],
                                        'card-img-top product-image'
                                    ); ?>

                                    <!-- Sale Badge -->
                                    <?php if ($item['sale_price']): ?>
                                        <div class="sale-badge">
                                            <i class="fas fa-tag me-1"></i>
                                            خصم <?php echo round((($item['price'] - $item['sale_price']) / $item['price']) * 100); ?>%
                                        </div>
                                    <?php endif; ?>

                                    <!-- Enhanced Remove Button -->
                                    <button class="wishlist-remove-btn"
                                            onclick="removeFromWishlist(<?php echo $item['id']; ?>)"
                                            title="إزالة من المفضلة">
                                        <i class="fas fa-heart-broken"></i>
                                    </button>


                                </div>
                                
                                <!-- Enhanced Product Info -->
                                <div class="card-body d-flex flex-column product-card-body">
                                    <h6 class="card-title product-title">
                                        <a href="../product.php?id=<?php echo $item['id']; ?>"
                                           class="text-decoration-none text-dark">
                                            <?php echo $item['name']; ?>
                                        </a>
                                    </h6>

                                    <p class="card-text text-muted small flex-grow-1">
                                        <?php echo substr($item['short_description'] ?: $item['description'], 0, 80); ?>...
                                    </p>

                                    <!-- Rating -->
                                    <div class="rating mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= 4 ? 'text-warning' : 'text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                        <small class="text-muted ms-2">(4.0)</small>
                                    </div>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div class="price product-price">
                                                <?php if ($item['sale_price']): ?>
                                                    <span class="text-danger fw-bold">
                                                        <?php echo format_price($item['sale_price']); ?>
                                                    </span>
                                                    <small class="text-muted text-decoration-line-through ms-2">
                                                        <?php echo format_price($item['price']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-primary fw-bold">
                                                        <?php echo format_price($item['price']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Stock Status -->
                                            <div class="stock-status">
                                                <?php if ($item['stock_quantity'] > 10): ?>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle ms-1"></i>
                                                        متوفر
                                                    </small>
                                                <?php elseif ($item['stock_quantity'] > 0): ?>
                                                    <small class="text-warning">
                                                        <i class="fas fa-exclamation-triangle ms-1"></i>
                                                        كمية محدودة
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-danger">
                                                        <i class="fas fa-times-circle ms-1"></i>
                                                        غير متوفر
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    
                                        <div class="d-grid gap-2">
                                            <?php if ($item['stock_quantity'] > 0): ?>
                                                <button type="button" class="btn add-to-cart-btn w-100"
                                                        onclick="addToCartFromWishlist(<?php echo $item['id']; ?>)">
                                                    <i class="fas fa-shopping-cart ms-2"></i>
                                                    أضف للسلة
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-secondary w-100" disabled>
                                                    <i class="fas fa-ban ms-2"></i>
                                                    غير متوفر
                                                </button>
                                            <?php endif; ?>

                                            <a href="../product.php?id=<?php echo $item['id']; ?>"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye ms-2"></i>
                                                عرض التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Continue Shopping -->
                <div class="text-center mt-5">
                    <a href="../" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-shopping-bag ms-2"></i>
                        متابعة التسوق
                    </a>
                </div>
                
            <?php else: ?>
                <!-- Enhanced Empty Wishlist -->
                <div class="wishlist-empty">
                    <div class="empty-icon">
                        <i class="fas fa-heart-broken fa-5x mb-4"></i>
                    </div>
                    <h3>المفضلة فارغة</h3>
                    <p>لم تقم بإضافة أي منتجات للمفضلة بعد.<br>ابدأ في استكشاف منتجاتنا المميزة!</p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="../" class="btn btn-primary btn-lg">
                            <i class="fas fa-shopping-bag me-2"></i>
                            ابدأ التسوق
                        </a>
                        <a href="../?featured=1" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-star me-2"></i>
                            المنتجات المميزة
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
    
    <?php include '../includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>js/script.js"></script>
    
    <script>
        // Enhanced remove from wishlist with animation
        function removeFromWishlist(productId) {
            const button = event.target.closest('button');
            const card = button.closest('.wishlist-item');

            // Add removing animation
            card.classList.add('removing');

            // Show confirmation with custom styling
            setTimeout(() => {
                if (confirm('هل تريد إزالة هذا المنتج من المفضلة؟')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="remove">
                        <input type="hidden" name="product_id" value="${productId}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                } else {
                    // Remove animation if cancelled
                    card.classList.remove('removing');
                }
            }, 300);
        }



        // Add to cart from wishlist
        function addToCartFromWishlist(productId) {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            // Add loading state
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';

            // Simulate API call
            setTimeout(() => {
                showWishlistToast('تم إضافة المنتج للسلة بنجاح', 'success');

                // Reset button
                button.disabled = false;
                button.innerHTML = originalText;

                // Add success animation
                button.style.animation = 'bounceIn 0.6s ease';
                setTimeout(() => {
                    button.style.animation = '';
                }, 600);
            }, 800);
        }

        // Enhanced toast function for wishlist
        function showWishlistToast(message, type = 'info') {
            let toastContainer = document.getElementById('wishlist-toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'wishlist-toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');

            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Initialize wishlist page
        document.addEventListener('DOMContentLoaded', function() {
            // Add staggered animation to wishlist items
            document.querySelectorAll('.wishlist-item').forEach((item, index) => {
                item.style.animationDelay = (index * 0.1) + 's';
                item.classList.add('animate-fade-in');
            });

            // Add smooth scroll to top when removing items
            window.addEventListener('beforeunload', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        });

        function addToCart(productId) {
            fetch('../api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const toast = document.createElement('div');
                    toast.className = 'toast-container position-fixed top-0 end-0 p-3';
                    toast.innerHTML = `
                        <div class="toast show" role="alert">
                            <div class="toast-header">
                                <i class="fas fa-check-circle text-success ms-2"></i>
                                <strong class="me-auto">تم بنجاح</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                تم إضافة المنتج للسلة
                            </div>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    
                    // Auto remove toast after 3 seconds
                    setTimeout(() => {
                        toast.remove();
                    }, 3000);
                    
                    // Update cart count if function exists
                    if (typeof updateCartCount === 'function') {
                        updateCartCount();
                    }
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الشبكة');
            });
        }
    </script>
</body>
</html>
