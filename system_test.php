<?php
/**
 * System Test - اختبار شامل للنظام
 * This file tests all major components of the Shoppy e-commerce system
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';
require_once 'classes/Category.php';
require_once 'classes/User.php';
require_once 'classes/Order.php';

// Initialize
$database = new Database();
$db = $database->getConnection();

$tests_passed = 0;
$tests_failed = 0;
$test_results = [];

function test_result($test_name, $result, $message = '') {
    global $tests_passed, $tests_failed, $test_results;
    
    if ($result) {
        $tests_passed++;
        $status = '✅ PASS';
        $class = 'success';
    } else {
        $tests_failed++;
        $status = '❌ FAIL';
        $class = 'danger';
    }
    
    $test_results[] = [
        'name' => $test_name,
        'status' => $status,
        'class' => $class,
        'message' => $message
    ];
}

// Test 1: Database Connection
try {
    $test_result = $db && $db->getAttribute(PDO::ATTR_CONNECTION_STATUS);
    test_result('Database Connection', $test_result, 'Database connection established successfully');
} catch (Exception $e) {
    test_result('Database Connection', false, 'Database connection failed: ' . $e->getMessage());
}

// Test 2: Required Files
$required_files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/navbar.php',
    'includes/footer.php',
    'classes/Product.php',
    'classes/Category.php',
    'classes/User.php',
    'classes/Order.php',
    'api/cart.php',
    'api/wishlist.php'
];

foreach ($required_files as $file) {
    test_result("File: $file", file_exists($file), file_exists($file) ? 'File exists' : 'File missing');
}

// Test 3: Required Directories
$required_dirs = [
    'assets/images/products',
    'assets/images/categories',
    'assets/css',
    'assets/js',
    'uploads/products',
    'uploads/categories',
    'logs'
];

foreach ($required_dirs as $dir) {
    test_result("Directory: $dir", is_dir($dir), is_dir($dir) ? 'Directory exists' : 'Directory missing');
}

// Test 4: Class Instantiation
try {
    $product = new Product($db);
    test_result('Product Class', true, 'Product class instantiated successfully');
} catch (Exception $e) {
    test_result('Product Class', false, 'Product class failed: ' . $e->getMessage());
}

try {
    $category = new Category($db);
    test_result('Category Class', true, 'Category class instantiated successfully');
} catch (Exception $e) {
    test_result('Category Class', false, 'Category class failed: ' . $e->getMessage());
}

try {
    $user = new User($db);
    test_result('User Class', true, 'User class instantiated successfully');
} catch (Exception $e) {
    test_result('User Class', false, 'User class failed: ' . $e->getMessage());
}

// Test 5: Functions
test_result('sanitize_input function', function_exists('sanitize_input'), 'Function exists');
test_result('format_price function', function_exists('format_price'), 'Function exists');
test_result('get_cart_count function', function_exists('get_cart_count'), 'Function exists');
test_result('generate_csrf_token function', function_exists('generate_csrf_token'), 'Function exists');

// Test 6: Constants
test_result('SITE_NAME constant', defined('SITE_NAME'), 'Constant defined: ' . (defined('SITE_NAME') ? SITE_NAME : 'Not defined'));
test_result('BASE_URL constant', defined('BASE_URL'), 'Constant defined: ' . (defined('BASE_URL') ? BASE_URL : 'Not defined'));
test_result('ASSETS_URL constant', defined('ASSETS_URL'), 'Constant defined: ' . (defined('ASSETS_URL') ? ASSETS_URL : 'Not defined'));

// Test 7: Session
session_start();
test_result('Session functionality', session_status() === PHP_SESSION_ACTIVE, 'Session is active');

// Test 8: Database Tables (if connection exists)
if ($db) {
    $tables = ['users', 'categories', 'products', 'product_images', 'orders', 'order_items', 'cart', 'wishlist'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT 1 FROM $table LIMIT 1");
            test_result("Table: $table", true, 'Table exists and accessible');
        } catch (Exception $e) {
            test_result("Table: $table", false, 'Table missing or inaccessible');
        }
    }
}

// Test 9: Image Placeholder
$placeholder_exists = file_exists('assets/images/placeholder.svg') || file_exists('assets/images/placeholder.png');
test_result('Image Placeholder', $placeholder_exists, 'Placeholder image exists');

// Test 10: API Endpoints
$api_files = ['cart.php', 'wishlist.php', 'search.php', 'search-suggestions.php'];
foreach ($api_files as $api_file) {
    $path = "api/$api_file";
    test_result("API: $api_file", file_exists($path), file_exists($path) ? 'API file exists' : 'API file missing');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - اختبار النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .test-summary { font-size: 1.2rem; font-weight: bold; }
        .test-item { border-left: 4px solid #dee2e6; }
        .test-item.success { border-left-color: #28a745; }
        .test-item.danger { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="test-header p-4 mb-4">
                    <h1 class="mb-0">🧪 Shoppy System Test</h1>
                    <p class="mb-0">اختبار شامل لجميع مكونات النظام</p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $tests_passed; ?></h3>
                                <p class="card-text">Tests Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-danger"><?php echo $tests_failed; ?></h3>
                                <p class="card-text">Tests Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo count($test_results); ?></h3>
                                <p class="card-text">Total Tests</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Test Results - نتائج الاختبار</h3>
                    </div>
                    <div class="card-body">
                        <?php foreach ($test_results as $test): ?>
                            <div class="test-item <?php echo $test['class']; ?> p-3 mb-2 bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?php echo htmlspecialchars($test['name']); ?></strong>
                                        <?php if ($test['message']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($test['message']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <span class="badge bg-<?php echo $test['class']; ?>"><?php echo $test['status']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="index.php" class="btn btn-primary btn-lg">العودة للموقع</a>
                    <button onclick="location.reload()" class="btn btn-secondary btn-lg">إعادة الاختبار</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
