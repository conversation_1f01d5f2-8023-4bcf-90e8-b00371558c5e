-- Shoppy E-Commerce Database Schema
-- قاعدة بيانات متجر شوبي الإلكتروني

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Database: shoppy_db
CREATE DATABASE IF NOT EXISTS `shoppy_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `shoppy_db`;

-- --------------------------------------------------------

-- Table structure for table `categories`
-- هيكل جدول التصنيفات

CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`),
  FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `users`
-- هيكل جدول المستخدمين

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Saudi Arabia',
  `role` enum('customer','admin') DEFAULT 'customer',
  `is_active` tinyint(1) DEFAULT 1,
  `email_verified` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `role` (`role`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `products`
-- هيكل جدول المنتجات

CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `sku` varchar(100) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `stock_quantity` int(11) DEFAULT 0,
  `category_id` int(11) NOT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `dimensions` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_featured` tinyint(1) DEFAULT 0,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`),
  KEY `category_id` (`category_id`),
  KEY `is_active` (`is_active`),
  KEY `is_featured` (`is_featured`),
  KEY `price` (`price`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `product_images`
-- هيكل جدول صور المنتجات

CREATE TABLE `product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `is_primary` tinyint(1) DEFAULT 0,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `is_primary` (`is_primary`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `product_variants`
-- هيكل جدول متغيرات المنتجات

CREATE TABLE `product_variants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `attributes` varchar(255) NOT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `stock_quantity` int(11) DEFAULT 0,
  `sku` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `is_active` (`is_active`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `orders`
-- هيكل جدول الطلبات

CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` enum('pending','processing','shipped','delivered','cancelled') DEFAULT 'pending',
  `total_amount` decimal(10,2) NOT NULL,
  `shipping_cost` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT 'cod',
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `shipping_address` text NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `notes` text DEFAULT NULL,
  `shipped_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `payment_status` (`payment_status`),
  KEY `created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `order_items`
-- هيكل جدول عناصر الطلبات

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `product_sku` varchar(100) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `product_attributes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`variant_id`) REFERENCES `product_variants` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `homepage_sections`
-- هيكل جدول أقسام الصفحة الرئيسية

CREATE TABLE `homepage_sections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_type` varchar(50) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `link_url` varchar(255) DEFAULT NULL,
  `link_text` varchar(100) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `section_type` (`section_type`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Insert sample data
-- إدراج بيانات تجريبية

-- Sample categories
INSERT INTO `categories` (`name`, `description`, `sort_order`, `is_active`) VALUES
('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية', 1, 1),
('ملابس', 'ملابس رجالية ونسائية وأطفال', 2, 1),
('منزل ومطبخ', 'أدوات منزلية ومطبخية', 3, 1),
('رياضة وترفيه', 'معدات رياضية وألعاب', 4, 1),
('كتب ومكتبة', 'كتب ومواد تعليمية', 5, 1),
('صحة وجمال', 'منتجات العناية والجمال', 6, 1);

-- Sample subcategories
INSERT INTO `categories` (`name`, `description`, `parent_id`, `sort_order`, `is_active`) VALUES
('هواتف ذكية', 'هواتف محمولة وإكسسوارات', 1, 1, 1),
('حاسوب محمول', 'أجهزة كمبيوتر محمولة', 1, 2, 1),
('ملابس رجالية', 'ملابس للرجال', 2, 1, 1),
('ملابس نسائية', 'ملابس للنساء', 2, 2, 1);

-- Sample admin user
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `phone`, `role`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير', 'النظام', '+966501234567', 'admin', 1);

-- Sample customer user
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `phone`, `address`, `city`, `role`, `is_active`) VALUES
('customer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'محمد', '+966507654321', 'شارع الملك فهد، حي النزهة', 'الرياض', 'customer', 1);

-- Sample products
INSERT INTO `products` (`name`, `description`, `short_description`, `sku`, `price`, `sale_price`, `stock_quantity`, `category_id`, `is_active`, `is_featured`) VALUES
('iPhone 15 Pro', 'هاتف آيفون 15 برو بأحدث التقنيات وكاميرا متطورة', 'هاتف آيفون 15 برو - 128GB', 'IPHONE15PRO-128', 4999.00, 4499.00, 25, 7, 1, 1),
('Samsung Galaxy S24', 'هاتف سامسونج جالاكسي S24 بمعالج قوي وشاشة رائعة', 'هاتف سامسونج جالاكسي S24', 'GALAXY-S24', 3999.00, NULL, 30, 7, 1, 1),
('MacBook Air M2', 'جهاز ماك بوك اير بمعالج M2 للأداء الفائق', 'ماك بوك اير M2 - 256GB SSD', 'MACBOOK-AIR-M2', 6999.00, 6499.00, 15, 8, 1, 1),
('قميص قطني رجالي', 'قميص قطني عالي الجودة مناسب للاستخدام اليومي', 'قميص قطني مريح وأنيق', 'SHIRT-COTTON-M', 89.00, 69.00, 50, 9, 1, 0),
('فستان صيفي نسائي', 'فستان صيفي خفيف ومريح بألوان زاهية', 'فستان صيفي أنيق ومريح', 'DRESS-SUMMER-L', 149.00, NULL, 35, 10, 1, 1),
('كتاب البرمجة بـ PHP', 'دليل شامل لتعلم البرمجة بلغة PHP من الصفر', 'تعلم PHP خطوة بخطوة', 'BOOK-PHP-2024', 79.00, 59.00, 100, 5, 1, 0);

-- Sample product images
INSERT INTO `product_images` (`product_id`, `image_path`, `alt_text`, `is_primary`, `sort_order`) VALUES
(1, 'iphone15pro-1.jpg', 'iPhone 15 Pro - الواجهة الأمامية', 1, 1),
(1, 'iphone15pro-2.jpg', 'iPhone 15 Pro - الواجهة الخلفية', 0, 2),
(2, 'galaxy-s24-1.jpg', 'Samsung Galaxy S24', 1, 1),
(3, 'macbook-air-m2.jpg', 'MacBook Air M2', 1, 1),
(4, 'shirt-cotton.jpg', 'قميص قطني رجالي', 1, 1),
(5, 'dress-summer.jpg', 'فستان صيفي نسائي', 1, 1),
(6, 'book-php.jpg', 'كتاب البرمجة بـ PHP', 1, 1);

-- Sample product variants
INSERT INTO `product_variants` (`product_id`, `attributes`, `price`, `stock_quantity`, `sku`) VALUES
(1, 'اللون: أزرق تيتانيوم، السعة: 128GB', 4499.00, 10, 'IPHONE15PRO-128-BLUE'),
(1, 'اللون: أزرق تيتانيوم، السعة: 256GB', 5199.00, 8, 'IPHONE15PRO-256-BLUE'),
(1, 'اللون: تيتانيوم طبيعي، السعة: 128GB', 4499.00, 7, 'IPHONE15PRO-128-NATURAL'),
(4, 'المقاس: M، اللون: أبيض', 69.00, 20, 'SHIRT-M-WHITE'),
(4, 'المقاس: L، اللون: أبيض', 69.00, 15, 'SHIRT-L-WHITE'),
(4, 'المقاس: M، اللون: أزرق', 69.00, 15, 'SHIRT-M-BLUE');

-- Sample homepage sections
INSERT INTO `homepage_sections` (`section_type`, `title`, `content`, `link_url`, `link_text`, `sort_order`, `is_active`) VALUES
('hero', 'مرحباً بك في متجر شوبي', 'اكتشف أفضل المنتجات بأسعار مميزة وجودة عالية', '#products', 'تسوق الآن', 1, 1),
('banner', 'عروض خاصة', 'خصومات تصل إلى 50% على مختارات من المنتجات', 'products.php?sale=1', 'اكتشف العروض', 2, 1),
('features', 'لماذا تختار شوبي؟', 'شحن مجاني، ضمان الجودة، خدمة عملاء ممتازة', NULL, NULL, 3, 1);

-- Sample orders
INSERT INTO `orders` (`order_number`, `user_id`, `status`, `total_amount`, `shipping_cost`, `tax_amount`, `shipping_address`, `customer_name`, `customer_email`, `customer_phone`, `notes`) VALUES
('ORD-2024-001', 2, 'delivered', 4724.00, 25.00, 674.85, 'شارع الملك فهد، حي النزهة، الرياض', 'أحمد محمد', '<EMAIL>', '+966507654321', 'يرجى التوصيل في المساء'),
('ORD-2024-002', 2, 'shipped', 158.00, 25.00, 19.95, 'شارع الملك فهد، حي النزهة، الرياض', 'أحمد محمد', '<EMAIL>', '+966507654321', NULL),
('ORD-2024-003', NULL, 'pending', 6724.00, 0.00, 974.85, 'شارع العليا، الرياض', 'سارة أحمد', '<EMAIL>', '+966501111111', 'طلب عاجل');

-- Sample order items
INSERT INTO `order_items` (`order_id`, `product_id`, `variant_id`, `product_name`, `product_sku`, `quantity`, `unit_price`, `total_price`) VALUES
(1, 1, 1, 'iPhone 15 Pro', 'IPHONE15PRO-128-BLUE', 1, 4499.00, 4499.00),
(2, 4, 4, 'قميص قطني رجالي', 'SHIRT-M-WHITE', 2, 69.00, 138.00),
(3, 3, NULL, 'MacBook Air M2', 'MACBOOK-AIR-M2', 1, 6499.00, 6499.00);

-- --------------------------------------------------------

-- Create indexes for better performance
-- إنشاء فهارس لتحسين الأداء

CREATE INDEX idx_products_category_active ON products(category_id, is_active);
CREATE INDEX idx_products_featured ON products(is_featured, is_active);
CREATE INDEX idx_products_price_range ON products(price, sale_price);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_orders_date_status ON orders(created_at, status);

-- --------------------------------------------------------

-- Create views for common queries
-- إنشاء عروض للاستعلامات الشائعة

-- View for products with category information
CREATE VIEW `products_with_category` AS
SELECT 
    p.*,
    c.name as category_name,
    c.parent_id as category_parent_id,
    (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
FROM products p
LEFT JOIN categories c ON p.category_id = c.id;

-- View for order summary
CREATE VIEW `order_summary` AS
SELECT 
    o.*,
    COUNT(oi.id) as item_count,
    u.username as user_username
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
LEFT JOIN users u ON o.user_id = u.id
GROUP BY o.id;

-- --------------------------------------------------------

-- Create triggers for automatic operations
-- إنشاء محفزات للعمليات التلقائية

DELIMITER $$

-- Trigger to generate order number
CREATE TRIGGER `generate_order_number` BEFORE INSERT ON `orders`
FOR EACH ROW BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        SET NEW.order_number = CONCAT('ORD-', YEAR(NOW()), '-', LPAD(FLOOR(RAND() * 999999), 6, '0'));
    END IF;
END$$

-- Trigger to update product stock after order
CREATE TRIGGER `update_stock_after_order` AFTER INSERT ON `order_items`
FOR EACH ROW BEGIN
    UPDATE products 
    SET stock_quantity = stock_quantity - NEW.quantity 
    WHERE id = NEW.product_id;
    
    IF NEW.variant_id IS NOT NULL THEN
        UPDATE product_variants 
        SET stock_quantity = stock_quantity - NEW.quantity 
        WHERE id = NEW.variant_id;
    END IF;
END$$

-- Trigger to restore stock when order is cancelled
CREATE TRIGGER `restore_stock_on_cancel` AFTER UPDATE ON `orders`
FOR EACH ROW BEGIN
    IF OLD.status != 'cancelled' AND NEW.status = 'cancelled' THEN
        UPDATE products p
        INNER JOIN order_items oi ON p.id = oi.product_id
        SET p.stock_quantity = p.stock_quantity + oi.quantity
        WHERE oi.order_id = NEW.id;
        
        UPDATE product_variants pv
        INNER JOIN order_items oi ON pv.id = oi.variant_id
        SET pv.stock_quantity = pv.stock_quantity + oi.quantity
        WHERE oi.order_id = NEW.id AND oi.variant_id IS NOT NULL;
    END IF;
END$$

DELIMITER ;

-- --------------------------------------------------------

COMMIT;
