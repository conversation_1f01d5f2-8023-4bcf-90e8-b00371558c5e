<?php
/**
 * Search API - واجهة برمجة تطبيقات البحث
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/Product.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize product class
$product_class = new Product($db);

$response = array('success' => false, 'suggestions' => array());

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $query = sanitize_input($_GET['q'] ?? '');
        
        if (strlen($query) >= 2) {
            $suggestions = $product_class->searchProducts($query, 1, 10);
            
            $formatted_suggestions = array();
            foreach ($suggestions as $product) {
                $formatted_suggestions[] = array(
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'price' => $product['sale_price'] ?: $product['price'],
                    'price_formatted' => format_price($product['sale_price'] ?: $product['price']),
                    'image' => $product['primary_image'],
                    'category' => $product['category_name'],
                    'url' => BASE_URL . 'product.php?id=' . $product['id']
                );
            }
            
            $response['success'] = true;
            $response['suggestions'] = $formatted_suggestions;
        }
    }
    
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ في البحث';
    error_log('Search API Error: ' . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
