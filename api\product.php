<?php
/**
 * Product API - واجهة برمجة تطبيقات المنتجات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/image_helper.php';
require_once '../classes/Product.php';

$response = array('success' => false, 'message' => '');

try {
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    $product_class = new Product($db);
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $product_id = (int)($_GET['id'] ?? 0);
        
        if ($product_id > 0) {
            $product = $product_class->getProductById($product_id);
            
            if ($product) {
                // Get product images
                $images = $product_class->getProductImages($product_id);
                
                // Format response
                $response['success'] = true;
                $response['product'] = [
                    'id' => $product['id'],
                    'name' => $product['name'],
                    'description' => $product['short_description'] ?: substr($product['description'], 0, 200) . '...',
                    'price' => $product['price'],
                    'sale_price' => $product['sale_price'],
                    'formatted_price' => format_price($product['price']),
                    'formatted_sale_price' => $product['sale_price'] ? format_price($product['sale_price']) : null,
                    'stock_quantity' => $product['stock_quantity'],
                    'category_name' => $product['category_name'] ?? '',
                    'image_url' => get_product_image_url($product['primary_image']),
                    'images' => array_map(function($img) {
                        return [
                            'url' => get_product_image_url($img['image_path']),
                            'alt' => $img['alt_text'] ?: 'صورة المنتج',
                            'is_primary' => $img['is_primary']
                        ];
                    }, $images),
                    'rating' => 4.0, // Default rating
                    'reviews_count' => 0,
                    'is_available' => $product['stock_quantity'] > 0,
                    'stock_status' => $product['stock_quantity'] > 10 ? 'متوفر' :
                                    ($product['stock_quantity'] > 0 ? 'كمية محدودة' : 'غير متوفر'),
                    'stock_class' => $product['stock_quantity'] > 10 ? 'text-success' :
                                   ($product['stock_quantity'] > 0 ? 'text-warning' : 'text-danger'),
                    'discount_percentage' => $product['sale_price']
                        ? round((($product['price'] - $product['sale_price']) / $product['price']) * 100)
                        : 0
                ];
                
                $response['message'] = 'تم جلب بيانات المنتج بنجاح';
            } else {
                $response['message'] = 'المنتج غير موجود';
            }
        } else {
            $response['message'] = 'معرف المنتج غير صحيح';
        }
    } else {
        $response['message'] = 'طريقة الطلب غير مدعومة';
    }
    
} catch (Exception $e) {
    $response['message'] = 'حدث خطأ في الخادم: ' . $e->getMessage();
    error_log('Product API Error: ' . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
