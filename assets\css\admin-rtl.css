/**
 * <PERSON><PERSON> RTL Styles - أنماط الأدمن للعربية
 */

/* RTL Dropdown Improvements */
.dropdown-menu {
    right: 0 !important;
    left: auto !important;
    text-align: right;
}

.dropdown-menu[data-bs-popper] {
    right: 0 !important;
    left: auto !important;
}

.dropdown-item {
    text-align: right;
    padding: 0.5rem 1rem;
}

.dropdown-item i {
    margin-left: 0.5rem;
    margin-right: 0;
    width: 1rem;
    text-align: center;
}

/* RTL Button Groups */
.btn-group .btn i {
    margin-left: 0.25rem;
    margin-right: 0;
}

.btn-group .btn:first-child i {
    margin-left: 0;
    margin-right: 0.25rem;
}

/* RTL Form Controls */
.form-select {
    background-position: left 0.75rem center !important;
    padding-left: 2.25rem !important;
    padding-right: 0.75rem !important;
}

.form-control {
    text-align: right;
}

/* RTL Input Groups */
.input-group-text {
    border-left: 1px solid #ced4da;
    border-right: 0;
}

.input-group > :not(:first-child) {
    margin-right: -1px;
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group > :first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* RTL Table Actions */
.btn-action {
    margin: 0 0.125rem;
}

.btn-action i {
    margin-left: 0.25rem;
    margin-right: 0;
}

/* RTL Pagination */
.pagination {
    direction: ltr;
}

.page-link {
    margin-right: -1px;
    margin-left: 0;
}

.page-item:first-child .page-link {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.page-item:last-child .page-link {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* RTL Alerts */
.alert i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Cards */
.card-header i {
    margin-left: 0.5rem;
    margin-right: 0;
}

.card-title i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL List Groups */
.list-group-item i {
    margin-left: 0.5rem;
    margin-right: 0;
    width: 1.25rem;
    text-align: center;
}

/* RTL Breadcrumbs */
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    float: right;
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Modal */
.modal-header .btn-close {
    margin: -0.5rem 0 -0.5rem auto;
}

.modal-title i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Navbar */
.navbar-brand i {
    margin-left: 0.5rem;
    margin-right: 0;
}

.navbar-nav .nav-link i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Sidebar */
.sidebar .nav-link i {
    margin-left: 0.75rem;
    margin-right: 0;
    width: 1.25rem;
    text-align: center;
}

/* RTL Data Tables */
.table th,
.table td {
    text-align: right;
}

.table th:first-child,
.table td:first-child {
    text-align: center;
}

.table th:last-child,
.table td:last-child {
    text-align: center;
}

/* RTL Form Labels */
.form-label {
    text-align: right;
    display: block;
}

.form-check-label {
    margin-right: 0;
    margin-left: 1.25rem;
}

.form-check-input {
    float: right;
    margin-right: 0;
    margin-left: -1.5rem;
}

/* RTL Progress */
.progress {
    direction: ltr;
}

/* RTL Tooltips */
.tooltip {
    direction: rtl;
}

/* RTL File Upload */
.form-control[type="file"] {
    text-align: right;
}

/* RTL Search */
.search-form .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}

.search-form .btn {
    border-radius: 0 0.375rem 0.375rem 0;
}

/* RTL Responsive Tables */
@media (max-width: 768px) {
    .table-responsive {
        direction: rtl;
    }
    
    .table-responsive .table {
        direction: rtl;
    }
}

/* RTL Mobile Menu */
@media (max-width: 991px) {
    .navbar-collapse {
        text-align: right;
    }
    
    .navbar-nav {
        align-items: flex-end;
    }
}

/* Custom RTL Utilities */
.ms-auto-rtl {
    margin-right: auto !important;
    margin-left: 0 !important;
}

.me-auto-rtl {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.text-start-rtl {
    text-align: right !important;
}

.text-end-rtl {
    text-align: left !important;
}
