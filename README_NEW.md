# 🛒 Shoppy E-Commerce Platform
## متجر شوبي الإلكتروني

A modern, responsive, and feature-rich e-commerce platform built with PHP, MySQL, and Bootstrap. Designed specifically for Arabic markets with RTL support and bilingual capabilities.

## ✨ Features / المميزات

### 🎨 Frontend Features
- **Responsive Design** - تصميم متجاوب يعمل على جميع الأجهزة
- **RTL Support** - دعم كامل للغة العربية والكتابة من اليمين لليسار
- **Modern UI/UX** - واجهة مستخدم عصرية وسهلة الاستخدام
- **Product Catalog** - كتالوج منتجات مع تصنيفات متعددة
- **Advanced Search** - بحث متقدم مع اقتراحات فورية
- **Shopping Cart** - عربة تسوق متطورة مع حفظ الجلسة
- **Wishlist** - قائمة المفضلة للمنتجات
- **User Authentication** - نظام تسجيل دخول وإنشاء حسابات
- **Order Tracking** - تتبع الطلبات والحالات
- **Multiple Payment Methods** - طرق دفع متعددة

### 🔧 Backend Features
- **Admin Dashboard** - لوحة تحكم إدارية شاملة
- **Product Management** - إدارة المنتجات والتصنيفات
- **Order Management** - إدارة الطلبات والمبيعات
- **User Management** - إدارة المستخدمين والصلاحيات
- **Inventory Management** - إدارة المخزون والكميات
- **Reports & Analytics** - تقارير وإحصائيات مفصلة
- **Settings Management** - إدارة إعدادات الموقع

### 🚀 Technical Features
- **Clean Code Architecture** - بنية كود نظيفة ومنظمة
- **Security Features** - حماية من SQL Injection و XSS
- **Performance Optimized** - محسن للأداء والسرعة
- **SEO Friendly** - محسن لمحركات البحث
- **API Ready** - واجهات برمجية جاهزة
- **Database Optimized** - قاعدة بيانات محسنة مع فهارس

## 📋 Requirements / المتطلبات

- **PHP** 7.4 or higher
- **MySQL** 5.7 or higher  
- **Apache/Nginx** web server
- **mod_rewrite** enabled
- **GD Extension** for image processing
- **PDO Extension** for database

## 🚀 Installation / التثبيت

### 1. Clone Repository
```bash
git clone https://github.com/your-username/shoppy.git
cd shoppy
```

### 2. Database Setup
```sql
-- Import the database schema
mysql -u root -p < database/schema.sql
```

### 3. Configuration
```php
// Edit config/database.php
private $host = 'localhost';
private $db_name = 'shoppy_ecommerce';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. Permissions
```bash
chmod 755 uploads/
chmod 755 assets/images/
chmod 644 .htaccess
```

### 5. Test Installation
Visit: `http://your-domain/system_test.php`

## 🔐 Default Login / تسجيل الدخول الافتراضي

### Admin Account
- **Email:** <EMAIL>
- **Password:** password

### Customer Account  
- **Email:** <EMAIL>
- **Password:** password

## 📁 Project Structure / بنية المشروع

```
shoppy/
├── admin/              # Admin panel
├── api/               # API endpoints
├── assets/            # CSS, JS, Images
├── auth/              # Authentication
├── classes/           # PHP Classes
├── config/            # Configuration
├── database/          # Database files
├── includes/          # Common includes
├── logs/              # Error logs
├── uploads/           # User uploads
├── user/              # User dashboard
├── .htaccess          # Apache config
├── index.php          # Homepage
└── system_test.php    # System test
```

## 🔧 API Endpoints / نقاط الواجهة البرمجية

### Cart API
- `POST /api/cart.php` - Add/Remove/Update cart items
- `GET /api/cart.php?action=get_count` - Get cart count

### Wishlist API
- `POST /api/wishlist.php` - Add/Remove wishlist items
- `GET /api/wishlist.php?action=get_items` - Get wishlist items

### Search API
- `GET /api/search.php` - Search products
- `GET /api/search-suggestions.php` - Get search suggestions

## 🛡️ Security Features / مميزات الأمان

- **SQL Injection Protection** - حماية من حقن SQL
- **XSS Protection** - حماية من البرمجة النصية المتقاطعة
- **CSRF Protection** - حماية من تزوير الطلبات
- **Input Validation** - التحقق من صحة المدخلات
- **Password Hashing** - تشفير كلمات المرور
- **Session Security** - أمان الجلسات

## 🚀 Performance Features / مميزات الأداء

- **Database Indexing** - فهرسة قاعدة البيانات
- **Image Optimization** - تحسين الصور
- **Caching Headers** - رؤوس التخزين المؤقت
- **Gzip Compression** - ضغط Gzip
- **Lazy Loading** - التحميل التدريجي
- **CDN Ready** - جاهز لشبكة التوصيل

## 📱 Browser Support / دعم المتصفحات

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers

## 🤝 Contributing / المساهمة

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License / الترخيص

This project is licensed under the MIT License.

## 📞 Support / الدعم

For support, email <EMAIL> or create an issue on GitHub.

---

**Made with ❤️ for Arabic E-Commerce**
