/**
 * Shoppy E-Commerce Custom Styles
 * أنماط متجر شوبي المخصصة
 */

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Hero Section */
.hero-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-slider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-slider .container {
    position: relative;
    z-index: 1;
}

/* Enhanced Product Cards with Animations */
.product-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    position: relative;
    background: white;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.product-image {
    height: 250px;
    object-fit: cover;
    transition: all 0.4s ease;
    width: 100%;
    background-color: #f8f9fa;
    position: relative;
    z-index: 2;
}

.product-card:hover .product-image {
    transform: scale(1.1) rotate(1deg);
    filter: brightness(1.1) contrast(1.1);
}

/* Image placeholder for missing images */
.image-placeholder {
    width: 100%;
    height: 250px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    border-radius: 0;
}

.image-placeholder i {
    font-size: 3rem;
    opacity: 0.5;
}

/* RTL Dropdown Improvements */
.dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

.dropdown-menu[data-bs-popper] {
    right: 0 !important;
    left: auto !important;
}

/* RTL Icon Improvements */
.navbar-nav .nav-link i {
    margin-left: 0.5rem;
    margin-right: 0;
}

.btn i {
    margin-left: 0.25rem;
    margin-right: 0;
}

.btn i:first-child {
    margin-left: 0;
    margin-right: 0.25rem;
}

/* RTL List Group */
.list-group-item i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    float: right;
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Form Controls */
.form-select {
    background-position: left 0.75rem center;
    padding-left: 2.25rem;
    padding-right: 0.75rem;
}

/* RTL Input Groups */
.input-group-text {
    border-left: 1px solid #ced4da;
    border-right: 0;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* RTL Pagination */
.page-link {
    margin-right: -1px;
    margin-left: 0;
}

.page-item:first-child .page-link {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.page-item:last-child .page-link {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.card-body {
    padding: 1.5rem;
}

/* Category Cards */
.category-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Alerts */
.alert {
    border-radius: 15px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* Tables */
.table {
    border-radius: 15px;
    overflow: hidden;
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* Pagination */
.pagination .page-link {
    border-radius: 25px;
    margin: 0 0.25rem;
    border: 2px solid #dee2e6;
    color: #667eea;
    font-weight: 500;
}

.pagination .page-link:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination .page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
}

/* Footer */
footer {
    background: #2c3e50 !important;
}

footer a {
    transition: color 0.3s ease;
}

footer a:hover {
    color: #667eea !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slider {
        min-height: 300px;
        padding: 2rem 0;
    }
    
    .hero-slider h1 {
        font-size: 2rem;
    }
    
    .product-image {
        height: 200px;
    }
    
    .category-card {
        padding: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .hero-slider h1 {
        font-size: 1.75rem;
    }
    
    .product-image {
        height: 180px;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.shadow-custom {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.border-radius-custom {
    border-radius: 15px;
}

/* Price Display */
.price-original {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9em;
}

.price-sale {
    color: #dc3545;
    font-weight: bold;
}

.price-regular {
    color: #28a745;
    font-weight: bold;
}

/* Stock Status */
.stock-available {
    color: #28a745;
}

.stock-low {
    color: #ffc107;
}

.stock-out {
    color: #dc3545;
}

/* Search Highlight */
.search-highlight {
    background: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    .card {
        background: #2c3e50;
        color: white;
    }
    
    .form-control {
        background: #34495e;
        border-color: #495057;
        color: white;
    }
    
    .form-control:focus {
        background: #34495e;
        border-color: #667eea;
        color: white;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Enhanced Product Card Animations */
.product-card-body {
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

.product-card:hover .product-card-body {
    transform: translateY(-5px);
}

.product-title {
    transition: color 0.3s ease;
}

.product-card:hover .product-title {
    color: #667eea;
}

.product-price {
    transition: all 0.3s ease;
}

.product-card:hover .product-price {
    transform: scale(1.05);
    font-weight: bold;
}

/* Animated Add to Cart Button */
.add-to-cart-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    color: white;
    font-weight: 600;
}

.add-to-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.add-to-cart-btn:hover::before {
    left: 100%;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.add-to-cart-btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Enhanced Product Image Container */
.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
    height: 250px;
}

/* Product Card Image Slider */
.product-image-slider {
    height: 100%;
}

.product-card-swiper {
    height: 100%;
    width: 100%;
}

.product-card-swiper .swiper-slide {
    height: 100%;
}

.product-card-swiper .product-image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

/* Slider Navigation Buttons */
.product-slider-next,
.product-slider-prev {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    color: #667eea;
    font-size: 12px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.product-slider-next {
    right: 10px;
}

.product-slider-prev {
    left: 10px;
}

.product-card:hover .product-slider-next,
.product-card:hover .product-slider-prev {
    opacity: 1;
}

.product-slider-next:hover,
.product-slider-prev:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
}

/* Slider Pagination */
.product-slider-pagination {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.product-slider-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.7);
    opacity: 1;
    margin: 0 3px;
}

.product-slider-pagination .swiper-pagination-bullet-active {
    background: white;
    transform: scale(1.2);
}



/* Enhanced Wishlist Button */
.wishlist-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 42px;
    height: 42px;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: #666;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 25;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(15px);
    cursor: pointer;
}

.wishlist-btn:hover {
    background: rgba(255, 255, 255, 1);
    color: #e74c3c;
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    border-color: rgba(231, 76, 60, 0.3);
}

.wishlist-btn.active {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border-color: #e74c3c;
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
}

.wishlist-btn.active:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: scale(1.2) rotate(-5deg);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.6);
}

/* Wishlist Icon Animation */
.wishlist-btn i {
    transition: all 0.3s ease;
}

.wishlist-btn:hover i {
    transform: scale(1.1);
}

.wishlist-btn.active i {
    animation: heartPulse 0.6s ease;
}

@keyframes heartPulse {
    0% { transform: scale(1); }
    25% { transform: scale(1.3); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.25); }
    100% { transform: scale(1); }
}

/* Heart Beat Animation */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.25); }
    100% { transform: scale(1); }
}



/* Enhanced Product Card Body */
.product-card-body {
    padding: 1.5rem;
    position: relative;
}

.product-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.product-card:hover .product-title {
    color: #667eea;
    transform: translateX(-3px);
}

/* Enhanced Price Display */
.product-price {
    font-weight: 700;
    margin-bottom: 1rem;
}

.product-price .text-danger {
    font-size: 1.1rem;
}

.product-price .text-decoration-line-through {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Enhanced Stock Status */
.stock-status {
    transition: all 0.3s ease;
}

.product-card:hover .stock-status {
    transform: translateX(-2px);
}

/* Enhanced Rating Display */
.rating {
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.rating .fa-star {
    font-size: 0.9rem;
    margin-right: 2px;
}

.product-card:hover .rating {
    transform: scale(1.05);
}

/* Enhanced Add to Cart Button */
.add-to-cart-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.add-to-cart-btn:active {
    transform: translateY(0);
}

/* Button Loading Animation */
.add-to-cart-btn:disabled {
    opacity: 0.8;
    cursor: not-allowed;
}

/* Enhanced Sale Badge */
.sale-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
    animation: pulse 2s infinite;
}

/* Featured Badge */
.featured-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
    animation: sparkle 3s infinite;
}

/* When both sale and featured badges exist */
.product-image-container .sale-badge + .featured-badge {
    top: 50px; /* Position below sale badge */
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(243, 156, 18, 0.5);
    }
}

/* Enhanced Hero Slider Styles */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-swiper {
    height: 70vh;
    min-height: 500px;
}

.hero-slide {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-image {
    z-index: 1;
    position: relative;
}

.hero-image img {
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

/* Hero Navigation */
.hero-next,
.hero-prev {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.hero-next:hover,
.hero-prev:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.hero-next:after,
.hero-prev:after {
    font-size: 18px;
    font-weight: bold;
}

/* Hero Pagination */
.hero-pagination {
    bottom: 30px !important;
}

.hero-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 1;
    margin: 0 5px;
}

.hero-pagination .swiper-pagination-bullet-active {
    background: white;
    transform: scale(1.2);
}

/* Hero Animations */
@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-slide-right {
    animation: slideRight 0.8s ease forwards;
}

.animate-slide-left {
    animation: slideLeft 0.8s ease forwards;
}

/* Category Banners Styles */
.category-banner {
    position: relative;
    height: 300px;
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.category-banner:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.banner-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 2;
    background: rgba(0, 0, 0, 0.4);
    color: white;
    text-align: center;
}

.banner-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.banner-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.category-banner:hover .banner-image img {
    transform: scale(1.1);
}

.banner-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.banner-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-content p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-banner {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-banner:hover {
    background: white;
    color: #333;
    border-color: white;
    transform: translateY(-2px);
}

/* Individual Banner Themes */
.electronics-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.fashion-banner {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.home-banner {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced Global Responsive Design */

/* Large Screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    .hero-swiper {
        height: 75vh;
        min-height: 600px;
    }

    .product-card {
        transition: all 0.4s ease;
    }

    .product-card:hover {
        transform: translateY(-10px) scale(1.02);
    }
}

/* Medium Screens (992px to 1199px) */
@media (max-width: 1199px) {
    .hero-swiper {
        height: 65vh;
        min-height: 500px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .category-banner {
        height: 280px;
    }
}

/* Small Screens (768px to 991px) */
@media (max-width: 991px) {
    .hero-swiper {
        height: 60vh;
        min-height: 450px;
    }

    .hero-content {
        text-align: center;
        margin-bottom: 2rem;
    }

    .hero-content h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .hero-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        justify-content: center;
        gap: 0.5rem;
    }

    .hero-buttons .btn {
        font-size: 0.9rem;
        padding: 0.6rem 1.2rem;
    }

    .category-banner {
        height: 250px;
        margin-bottom: 1rem;
    }

    .banner-content {
        padding: 20px;
    }

    .banner-icon {
        font-size: 2.5rem;
    }

    .banner-content h3 {
        font-size: 1.5rem;
    }

    .banner-content p {
        font-size: 0.9rem;
    }

    .product-image-container {
        height: 220px;
    }

    .product-card-body {
        padding: 1.25rem;
    }

    .product-title {
        font-size: 1rem;
    }
}

/* Extra Small Screens (576px to 767px) */
@media (max-width: 767px) {
    .hero-swiper {
        height: 50vh;
        min-height: 400px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .hero-content p {
        font-size: 0.9rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 200px;
        margin: 0.25rem 0;
    }

    .hero-next,
    .hero-prev {
        width: 40px;
        height: 40px;
    }

    .hero-next:after,
    .hero-prev:after {
        font-size: 14px;
    }

    .category-banner {
        height: 220px;
    }

    .banner-content {
        padding: 15px;
    }

    .banner-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .banner-content h3 {
        font-size: 1.3rem;
        margin-bottom: 0.5rem;
    }

    .banner-content p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .btn-banner {
        padding: 8px 20px;
        font-size: 0.85rem;
    }

    .product-image-container {
        height: 200px;
    }

    .product-card-body {
        padding: 1rem;
    }

    .product-title {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .product-price {
        font-size: 0.9rem;
    }

    .rating {
        font-size: 0.85rem;
    }

    .add-to-cart-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
}

/* Very Small Screens (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .hero-swiper {
        height: 45vh;
        min-height: 350px;
    }

    .hero-content {
        padding: 1rem;
    }

    .hero-content h1 {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .hero-content p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .hero-buttons .btn {
        width: 180px;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .category-banner {
        height: 200px;
    }

    .banner-content {
        padding: 12px;
    }

    .banner-icon {
        font-size: 1.8rem;
    }

    .banner-content h3 {
        font-size: 1.1rem;
    }

    .banner-content p {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .product-image-container {
        height: 180px;
    }

    .product-card-body {
        padding: 0.75rem;
    }

    .product-title {
        font-size: 0.9rem;
    }

    .wishlist-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
        top: 8px;
        right: 8px;
    }

    .sale-badge,
    .featured-badge {
        padding: 6px 10px;
        font-size: 11px;
        top: 8px;
        left: 8px;
    }

    .product-slider-next,
    .product-slider-prev {
        width: 25px;
        height: 25px;
        font-size: 10px;
    }

    .product-slider-pagination .swiper-pagination-bullet {
        width: 6px;
        height: 6px;
        margin: 0 2px;
    }
}

/* Ultra Small Screens (up to 400px) */
@media (max-width: 400px) {
    .hero-content h1 {
        font-size: 1.3rem;
    }

    .hero-content p {
        font-size: 0.8rem;
    }

    .hero-buttons .btn {
        width: 160px;
        font-size: 0.75rem;
    }

    .category-banner {
        height: 180px;
    }

    .banner-content h3 {
        font-size: 1rem;
    }

    .banner-content p {
        font-size: 0.75rem;
    }

    .product-image-container {
        height: 160px;
    }

    .product-title {
        font-size: 0.85rem;
    }

    .add-to-cart-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .product-image-container {
        height: 200px;
    }

    .product-slider-next,
    .product-slider-prev {
        width: 25px;
        height: 25px;
        font-size: 10px;
    }

    .wishlist-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
        top: 10px;
        right: 10px;
    }

    .sale-badge {
        padding: 6px 10px;
        font-size: 11px;
        top: 10px;
        left: 10px;
    }

    .overlay-actions {
        gap: 8px;
    }

    .quick-view-btn,
    .quick-add-btn {
        padding: 8px 16px;
        font-size: 12px;
        min-width: 120px;
    }
}

@media (max-width: 576px) {
    .product-image-container {
        height: 180px;
    }

    .product-card-body {
        padding: 1rem;
    }

    .product-title {
        font-size: 0.95rem;
    }

    .quick-view-slider {
        height: 250px;
    }
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Improved Hover Effects */
.product-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.product-card:hover .product-image {
    transform: scale(1.08);
}

/* Ensure Product Cards are Always Visible */
.product-card {
    opacity: 1 !important;
    transform: none !important;
    transition: all 0.3s ease;
    visibility: visible !important;
}

/* Hover Effects Only */
.product-card:hover {
    transform: translateY(-8px) scale(1.02);
}

/* Enhanced Wishlist Page Styles */
.wishlist-remove-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 42px;
    height: 42px;
    background: rgba(231, 76, 60, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 25;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    backdrop-filter: blur(15px);
    cursor: pointer;
}

.wishlist-remove-btn:hover {
    background: rgba(192, 57, 43, 1);
    transform: scale(1.15) rotate(10deg);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
    border-color: rgba(255, 255, 255, 0.5);
}

.wishlist-remove-btn:active {
    transform: scale(1.05) rotate(5deg);
}

/* Wishlist Item Animations */
.wishlist-item {
    transition: all 0.3s ease;
}

.wishlist-item.removing {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
    pointer-events: none;
}

/* Enhanced Wishlist Empty State */
.wishlist-empty {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    margin: 2rem 0;
}

.wishlist-empty i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.wishlist-empty h3 {
    color: #6c757d;
    margin-bottom: 1rem;
}

.wishlist-empty p {
    color: #adb5bd;
    margin-bottom: 2rem;
}

.quick-view-btn:hover {
    background: #667eea;
    color: white;
    transform: scale(1.05);
}

/* Sale Badge Animation */
.sale-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 10;
    animation: pulse 2s infinite;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* Wishlist Heart Animation */
.wishlist-btn {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    transition: all 0.3s ease;
    z-index: 10;
    opacity: 0;
    transform: scale(0.8);
}

.product-card:hover .wishlist-btn {
    opacity: 1;
    transform: scale(1);
}

.wishlist-btn:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.wishlist-btn.active {
    background: #dc3545;
    color: white;
}

/* Loading Animation for Images */
.product-image-loading {
    position: relative;
}

.product-image-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-outline-primary {
        border-width: 2px;
    }

    .card {
        border: 2px solid #dee2e6;
    }
}

/* Enhanced Animation Keyframes */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation Classes */
.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

/* Staggered Animation for Product Cards */
.product-card:nth-child(1) { animation-delay: 0.1s; }
.product-card:nth-child(2) { animation-delay: 0.2s; }
.product-card:nth-child(3) { animation-delay: 0.3s; }
.product-card:nth-child(4) { animation-delay: 0.4s; }
.product-card:nth-child(5) { animation-delay: 0.5s; }
.product-card:nth-child(6) { animation-delay: 0.6s; }
.product-card:nth-child(7) { animation-delay: 0.7s; }
.product-card:nth-child(8) { animation-delay: 0.8s; }

/* Enhanced Hover Effects */
.product-card:hover .card-title {
    color: #667eea;
    transform: translateX(-5px);
}

.product-card:hover .rating {
    transform: scale(1.1);
}

.product-card:hover .stock-status {
    transform: translateX(-3px);
}

/* Image Loading Shimmer Effect */
.product-image-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

/* Toast Notifications */
.toast-container {
    z-index: 9999;
}

.toast {
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .product-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .product-image-overlay {
        opacity: 0.9;
    }

    .quick-view-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .wishlist-btn {
        width: 35px;
        height: 35px;
    }

    .sale-badge {
        padding: 6px 10px;
        font-size: 0.7rem;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
