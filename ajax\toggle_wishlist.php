<?php
/**
 * Toggle Wishlist - AJAX endpoint
 * تبديل المفضلة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['product_id']) || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

$product_id = (int)$input['product_id'];
$action = $input['action']; // 'add' or 'remove'
$user_id = $_SESSION['user_id'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($action === 'add') {
        // Add to wishlist
        $query = "INSERT IGNORE INTO wishlist (user_id, product_id, created_at) VALUES (?, ?, NOW())";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id, $product_id]);
        
        echo json_encode(['success' => true, 'message' => 'تمت الإضافة للمفضلة']);
        
    } elseif ($action === 'remove') {
        // Remove from wishlist
        $query = "DELETE FROM wishlist WHERE user_id = ? AND product_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id, $product_id]);
        
        echo json_encode(['success' => true, 'message' => 'تم الحذف من المفضلة']);
        
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في الخادم']);
}
?>
