/* Enhanced Shop/Catalog Styles - أنماط المتجر المحسنة */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* Hero Section Enhanced */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-search {
    max-width: 600px;
    margin: 0 auto;
}

.search-container {
    position: relative;
    background: white;
    border-radius: 50px;
    padding: 5px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.search-input {
    border: none;
    padding: 15px 25px;
    font-size: 1.1rem;
    border-radius: 50px;
    width: 100%;
    outline: none;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 5px;
    bottom: 5px;
    background: var(--primary-color);
    border: none;
    color: white;
    border-radius: 50px;
    padding: 0 25px;
    font-weight: 600;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* Categories Section */
.categories-section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--dark-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 2px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.category-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-decoration: none;
    color: var(--dark-color);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transition: left 0.5s ease;
    z-index: 1;
}

.category-card:hover::before {
    left: 0;
}

.category-card:hover {
    transform: translateY(-10px);
    color: white;
    text-decoration: none;
}

.category-card > * {
    position: relative;
    z-index: 2;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.category-card:hover .category-icon {
    color: white;
    transform: scale(1.2);
}

.category-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-count {
    color: #6c757d;
    font-size: 0.9rem;
    transition: var(--transition);
}

.category-card:hover .category-count {
    color: rgba(255,255,255,0.8);
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 3rem;
    position: sticky;
    top: 20px;
    z-index: 100;
}

.filters-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filters-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.filters-toggle {
    display: none;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
}

.filters-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.filter-select,
.filter-input {
    padding: 12px 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    font-size: 1rem;
    transition: var(--transition);
}

.filter-select:focus,
.filter-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 10px;
}

.btn-filter {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-filter:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-clear {
    background: white;
    color: var(--dark-color);
    border: 2px solid #dee2e6;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-clear:hover {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

/* Products Grid Enhanced */
.products-section {
    margin-bottom: 3rem;
}

.products-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.products-count {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sort-label {
    font-weight: 600;
    color: var(--dark-color);
}

.sort-select {
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    font-size: 1rem;
    min-width: 150px;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
}

.view-btn {
    background: none;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    color: #6c757d;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Product Card Enhanced */
.product-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    text-decoration: none;
    color: inherit;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.product-image-container {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 10;
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.badge-sale {
    background: var(--danger-color);
}

.badge-new {
    background: var(--success-color);
}

.badge-featured {
    background: var(--warning-color);
    color: var(--dark-color);
}

.product-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateX(20px);
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    background: white;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.stars {
    color: #ffc107;
    font-size: 0.9rem;
}

.rating-count {
    color: #6c757d;
    font-size: 0.8rem;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.price-current {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--success-color);
}

.price-original {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
}

.product-actions-bottom {
    display: flex;
    gap: 10px;
}

.btn-add-cart {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-add-cart:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-quick-view {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 12px 15px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-quick-view:hover {
    background: var(--primary-color);
    color: white;
}

/* List View Styles */
.product-card.list-view {
    display: flex;
    align-items: center;
    padding: 1.5rem;
}

.product-card.list-view .product-image-container {
    width: 150px;
    height: 150px;
    flex-shrink: 0;
    margin-left: 2rem;
}

.product-card.list-view .product-info {
    flex: 1;
    padding: 0 2rem;
}

.product-card.list-view .product-actions-bottom {
    flex-direction: column;
    min-width: 200px;
}

/* Pagination Enhanced */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination {
    display: flex;
    gap: 10px;
    align-items: center;
}

.page-link {
    padding: 12px 18px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.page-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
}

.page-link.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .filters-toggle {
        display: block;
    }
    
    .filters-content {
        display: none;
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .filters-content.show {
        display: grid;
    }
    
    .products-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .sort-controls {
        justify-content: space-between;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .product-card.list-view {
        flex-direction: column;
        text-align: center;
    }
    
    .product-card.list-view .product-image-container {
        width: 100%;
        height: 200px;
        margin: 0 0 1rem 0;
    }
    
    .product-card.list-view .product-info {
        padding: 0;
    }
    
    .product-card.list-view .product-actions-bottom {
        min-width: auto;
        flex-direction: row;
    }
}
