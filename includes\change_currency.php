<?php
/**
 * Change Currency Handler
 * معالج تغيير العملة
 */

require_once '../config/config.php';
require_once 'functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['currency'])) {
    $currency = $_POST['currency'];

    if (set_current_currency($currency)) {
        // Check if this is an AJAX request
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            // Return JSON response for AJAX
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'currency' => $currency]);
            exit();
        } else {
            // Regular form submission - redirect back
            $redirect_url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : BASE_URL;
            header('Location: ' . $redirect_url);
            exit();
        }
    }
}

// If something went wrong
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid currency']);
} else {
    header('Location: ' . BASE_URL);
}
exit();
?>
