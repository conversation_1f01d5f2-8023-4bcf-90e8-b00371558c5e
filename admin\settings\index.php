<?php
/**
 * Admin Settings Management - إدارة إعدادات الموقع
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_settings'])) {
        $settings = [
            'site_name' => sanitize_input($_POST['site_name']),
            'site_description' => sanitize_input($_POST['site_description']),
            'site_keywords' => sanitize_input($_POST['site_keywords']),
            'contact_email' => sanitize_input($_POST['contact_email']),
            'contact_phone' => sanitize_input($_POST['contact_phone']),
            'contact_address' => sanitize_input($_POST['contact_address']),
            'currency' => sanitize_input($_POST['currency']),
            'currency_symbol' => sanitize_input($_POST['currency_symbol']),
            'tax_rate' => (float)$_POST['tax_rate'],
            'shipping_cost' => (float)$_POST['shipping_cost'],
            'free_shipping_threshold' => (float)$_POST['free_shipping_threshold'],
            'products_per_page' => (int)$_POST['products_per_page'],
            'enable_reviews' => isset($_POST['enable_reviews']) ? 1 : 0,
            'enable_wishlist' => isset($_POST['enable_wishlist']) ? 1 : 0,
            'enable_notifications' => isset($_POST['enable_notifications']) ? 1 : 0,
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
            'facebook_url' => sanitize_input($_POST['facebook_url']),
            'twitter_url' => sanitize_input($_POST['twitter_url']),
            'instagram_url' => sanitize_input($_POST['instagram_url']),
            'whatsapp_number' => sanitize_input($_POST['whatsapp_number'])
        ];
        
        $success_count = 0;
        foreach ($settings as $key => $value) {
            $query = "INSERT INTO settings (setting_key, setting_value) VALUES (:key, :value) 
                      ON DUPLICATE KEY UPDATE setting_value = :value";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            if ($stmt->execute()) {
                $success_count++;
            }
        }
        
        if ($success_count > 0) {
            $success_message = "تم تحديث الإعدادات بنجاح!";
        } else {
            $error_message = "حدث خطأ أثناء تحديث الإعدادات!";
        }
    }
}

// Get current settings
function getSetting($key, $default = '') {
    global $db;
    $query = "SELECT setting_value FROM settings WHERE setting_key = :key";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':key', $key);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result ? $result['setting_value'] : $default;
}

$page_title = "إعدادات الموقع - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .settings-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        
        .settings-body {
            padding: 2rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-check-input {
            transform: scale(1.2);
        }
        
        .btn-save {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 0.25rem;
            font-weight: 500;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .tab-content {
            margin-top: 2rem;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px 0 0 10px;
        }
        
        .input-group .form-control {
            border-radius: 0 10px 10px 0;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                    <h4 class="mb-0">إعدادات الموقع</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync ms-2"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle ms-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Settings Navigation -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills justify-content-center mb-4" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog ms-2"></i>
                            إعدادات عامة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="store-tab" data-bs-toggle="pill" data-bs-target="#store" type="button" role="tab">
                            <i class="fas fa-store ms-2"></i>
                            إعدادات المتجر
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="pill" data-bs-target="#contact" type="button" role="tab">
                            <i class="fas fa-address-book ms-2"></i>
                            معلومات الاتصال
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="social-tab" data-bs-toggle="pill" data-bs-target="#social" type="button" role="tab">
                            <i class="fas fa-share-alt ms-2"></i>
                            وسائل التواصل
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="advanced-tab" data-bs-toggle="pill" data-bs-target="#advanced" type="button" role="tab">
                            <i class="fas fa-tools ms-2"></i>
                            إعدادات متقدمة
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <form method="POST">
            <div class="tab-content" id="settingsTabContent">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general" role="tabpanel">
                    <div class="settings-card card">
                        <div class="settings-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog ms-2"></i>
                                الإعدادات العامة
                            </h5>
                        </div>
                        <div class="settings-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">اسم الموقع</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name"
                                               value="<?php echo htmlspecialchars(getSetting('site_name', SITE_NAME)); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email"
                                               value="<?php echo htmlspecialchars(getSetting('contact_email')); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="site_description" class="form-label">وصف الموقع</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars(getSetting('site_description')); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="site_keywords" class="form-label">الكلمات المفتاحية</label>
                                <input type="text" class="form-control" id="site_keywords" name="site_keywords"
                                       value="<?php echo htmlspecialchars(getSetting('site_keywords')); ?>"
                                       placeholder="متجر إلكتروني، تسوق، منتجات">
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="products_per_page" class="form-label">المنتجات في الصفحة</label>
                                        <select class="form-select" id="products_per_page" name="products_per_page">
                                            <option value="12" <?php echo getSetting('products_per_page', '12') == '12' ? 'selected' : ''; ?>>12</option>
                                            <option value="24" <?php echo getSetting('products_per_page', '12') == '24' ? 'selected' : ''; ?>>24</option>
                                            <option value="36" <?php echo getSetting('products_per_page', '12') == '36' ? 'selected' : ''; ?>>36</option>
                                            <option value="48" <?php echo getSetting('products_per_page', '12') == '48' ? 'selected' : ''; ?>>48</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <label class="form-label">المميزات</label>
                                    <div class="d-flex gap-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable_reviews" name="enable_reviews"
                                                   <?php echo getSetting('enable_reviews', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_reviews">تفعيل التقييمات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable_wishlist" name="enable_wishlist"
                                                   <?php echo getSetting('enable_wishlist', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_wishlist">تفعيل المفضلة</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications"
                                                   <?php echo getSetting('enable_notifications', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_notifications">تفعيل الإشعارات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Store Settings -->
                <div class="tab-pane fade" id="store" role="tabpanel">
                    <div class="settings-card card">
                        <div class="settings-header">
                            <h5 class="mb-0">
                                <i class="fas fa-store ms-2"></i>
                                إعدادات المتجر
                            </h5>
                        </div>
                        <div class="settings-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="SAR" <?php echo getSetting('currency', 'SAR') == 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                            <option value="USD" <?php echo getSetting('currency', 'SAR') == 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                            <option value="EUR" <?php echo getSetting('currency', 'SAR') == 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                            <option value="AED" <?php echo getSetting('currency', 'SAR') == 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_symbol" class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                               value="<?php echo htmlspecialchars(getSetting('currency_symbol', 'ر.س')); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate"
                                                   value="<?php echo getSetting('tax_rate', '15'); ?>" step="0.01" min="0" max="100">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="shipping_cost" class="form-label">تكلفة الشحن</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="shipping_cost" name="shipping_cost"
                                                   value="<?php echo getSetting('shipping_cost', '25'); ?>" step="0.01" min="0">
                                            <span class="input-group-text"><?php echo getSetting('currency_symbol', 'ر.س'); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="free_shipping_threshold" class="form-label">حد الشحن المجاني</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="free_shipping_threshold" name="free_shipping_threshold"
                                                   value="<?php echo getSetting('free_shipping_threshold', '200'); ?>" step="0.01" min="0">
                                            <span class="input-group-text"><?php echo getSetting('currency_symbol', 'ر.س'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Settings -->
                <div class="tab-pane fade" id="contact" role="tabpanel">
                    <div class="settings-card card">
                        <div class="settings-header">
                            <h5 class="mb-0">
                                <i class="fas fa-address-book ms-2"></i>
                                معلومات الاتصال
                            </h5>
                        </div>
                        <div class="settings-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                               value="<?php echo htmlspecialchars(getSetting('contact_phone')); ?>"
                                               placeholder="+966 50 123 4567">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="whatsapp_number" class="form-label">رقم الواتساب</label>
                                        <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number"
                                               value="<?php echo htmlspecialchars(getSetting('whatsapp_number')); ?>"
                                               placeholder="966501234567">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="contact_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="contact_address" name="contact_address" rows="3"><?php echo htmlspecialchars(getSetting('contact_address')); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Settings -->
                <div class="tab-pane fade" id="social" role="tabpanel">
                    <div class="settings-card card">
                        <div class="settings-header">
                            <h5 class="mb-0">
                                <i class="fas fa-share-alt ms-2"></i>
                                وسائل التواصل الاجتماعي
                            </h5>
                        </div>
                        <div class="settings-body">
                            <div class="mb-3">
                                <label for="facebook_url" class="form-label">رابط الفيسبوك</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                           value="<?php echo htmlspecialchars(getSetting('facebook_url')); ?>"
                                           placeholder="https://facebook.com/yourpage">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="twitter_url" class="form-label">رابط تويتر</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                           value="<?php echo htmlspecialchars(getSetting('twitter_url')); ?>"
                                           placeholder="https://twitter.com/youraccount">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="instagram_url" class="form-label">رابط الإنستغرام</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                           value="<?php echo htmlspecialchars(getSetting('instagram_url')); ?>"
                                           placeholder="https://instagram.com/youraccount">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings -->
                <div class="tab-pane fade" id="advanced" role="tabpanel">
                    <div class="settings-card card">
                        <div class="settings-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tools ms-2"></i>
                                الإعدادات المتقدمة
                            </h5>
                        </div>
                        <div class="settings-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle ms-2"></i>
                                <strong>تحذير:</strong> هذه الإعدادات للمستخدمين المتقدمين فقط. تغييرها قد يؤثر على عمل الموقع.
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode"
                                       <?php echo getSetting('maintenance_mode', '0') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenance_mode">
                                    <strong>وضع الصيانة</strong>
                                    <br><small class="text-muted">عند التفعيل، سيظهر للزوار صفحة صيانة فقط</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="text-center mt-4 mb-5">
                <button type="submit" name="update_settings" class="btn btn-save btn-lg">
                    <i class="fas fa-save ms-2"></i>
                    حفظ جميع الإعدادات
                </button>
            </div>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-save notification
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-save');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ms-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;

            // Re-enable after form submission
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });

        // Currency symbol auto-update
        document.getElementById('currency').addEventListener('change', function() {
            const currencySymbols = {
                'SAR': 'ر.س',
                'USD': '$',
                'EUR': '€',
                'AED': 'د.إ'
            };

            const selectedCurrency = this.value;
            const symbolInput = document.getElementById('currency_symbol');

            if (currencySymbols[selectedCurrency]) {
                symbolInput.value = currencySymbols[selectedCurrency];
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const siteName = document.getElementById('site_name').value.trim();

            if (!siteName) {
                e.preventDefault();
                alert('يرجى إدخال اسم الموقع');
                document.getElementById('site_name').focus();
                return false;
            }
        });
    </script>
</body>
</html>
