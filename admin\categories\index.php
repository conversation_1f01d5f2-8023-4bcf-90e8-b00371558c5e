<?php
/**
 * Admin Categories Management - إدارة التصنيفات
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Category.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize category class
$category_class = new Category($db);

// Handle category actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['delete_category'])) {
        $category_id = (int)$_POST['category_id'];
        if ($category_class->deleteCategory($category_id)) {
            $success_message = "تم حذف التصنيف بنجاح!";
        } else {
            $error_message = "لا يمكن حذف التصنيف لأنه يحتوي على منتجات!";
        }
    }
}

// Get categories with product count
$categories = $category_class->getCategoriesWithProductCount();

$page_title = "إدارة التصنيفات - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .category-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .category-card:hover {
            transform: translateY(-2px);
        }
        
        .category-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .category-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        .btn-action {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            margin: 0 0.125rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 0;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i> </a>
                    <h4 class="mb-0">إدارة التصنيفات</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus ms-2 ms-1"></i>إضافة تصنيف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle ms-2 ms-1"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2 ms-1"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Categories List -->
        <?php if (!empty($categories)): ?>
            <div class="row">
                <?php foreach ($categories as $category): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="category-card card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-start mb-3">
                                    <!-- Category Image -->
                                    <div class="ms-3">
                                        <?php if ($category['image']): ?>
                                            <img src="<?php echo ASSETS_URL; ?>images/categories/<?php echo $category['image']; ?>" 
                                                 class="category-image" alt="<?php echo $category['name']; ?>">
                                        <?php else: ?>
                                            <div class="category-image bg-light d-flex align-items-center justify-content-center">
                                                <i class="fas fa-folder fa-2x text-muted ms-1"></i> </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Category Info -->
                                    <div class="flex-grow-1">
                                        <h5 class="card-title mb-2"><?php echo $category['name']; ?></h5>
                                        
                                        <?php if ($category['description']): ?>
                                            <p class="card-text text-muted small">
                                                <?php echo substr($category['description'], 0, 100); ?>
                                                <?php echo strlen($category['description']) > 100 ? '...' : ''; ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="category-status <?php echo $category['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                <?php echo $category['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                            
                                            <small class="text-muted">
                                                <i class="fas fa-box ms-1 ms-1"></i> <?php echo $category['product_count']; ?> منتج
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Actions -->
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-1">
                                        <a href="edit.php?id=<?php echo $category['id']; ?>" 
                                           class="btn btn-outline-primary btn-action" title="تعديل">
                                            <i class="fas fa-edit ms-1"></i> </a>
                                        
                                        <a href="../products/?category=<?php echo $category['id']; ?>" 
                                           class="btn btn-outline-info btn-action" title="عرض المنتجات">
                                            <i class="fas fa-box ms-1"></i> </a>
                                        
                                        <?php if ($category['product_count'] == 0): ?>
                                            <button type="button" class="btn btn-outline-danger btn-action" 
                                                    onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo addslashes($category['name']); ?>')" 
                                                    title="حذف">
                                                <i class="fas fa-trash ms-1"></i> </button>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <small class="text-muted">
                                        ترتيب: <?php echo $category['sort_order']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-tags fa-5x text-muted mb-4 ms-1"></i> <h3>لا توجد تصنيفات</h3>
                <p class="text-muted mb-4">لم يتم إضافة أي تصنيفات بعد</p>
                <a href="add.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus ms-2 ms-1"></i>إضافة تصنيف جديد
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف التصنيف "<span id="categoryName"></span>"؟</p>
                    <p class="text-danger small">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="category_id" id="deleteCategoryId">
                        <button type="submit" name="delete_category" class="btn btn-danger">
                            <i class="fas fa-trash ms-2 ms-1"></i>حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Delete category function
        function deleteCategory(categoryId, categoryName) {
            document.getElementById('categoryName').textContent = categoryName;
            document.getElementById('deleteCategoryId').value = categoryId;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
</body>
</html>
