<?php
/**
 * Edit Product Page - صفحة تعديل المنتج
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/image_helper.php';
require_once '../../classes/Product.php';
require_once '../../classes/Category.php';

// Check admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$category = new Category($db);

// Get product ID
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$product_id) {
    header('Location: index.php');
    exit();
}

// Get product details
$product = $product_class->getProductById($product_id);
if (!$product) {
    header('Location: index.php');
    exit();
}

// Get product images
$product_images = $product_class->getProductImages($product_id);

// Get categories for dropdown
$categories = $category->getAllCategories();

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_product'])) {
        $name = trim($_POST['name']);
        $description = trim($_POST['description']);
        $short_description = trim($_POST['short_description']);
        $sku = trim($_POST['sku']);
        $price = (float)$_POST['price'];
        $sale_price = !empty($_POST['sale_price']) ? (float)$_POST['sale_price'] : null;
        $stock_quantity = (int)$_POST['stock_quantity'];
        $category_id = (int)$_POST['category_id'];
        $weight = !empty($_POST['weight']) ? (float)$_POST['weight'] : null;
        $dimensions = trim($_POST['dimensions']);
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;
        $meta_title = trim($_POST['meta_title']);
        $meta_description = trim($_POST['meta_description']);
        
        // Validation
        if (empty($name) || empty($price) || empty($category_id)) {
            $error_message = "يرجى ملء جميع الحقول المطلوبة";
        } else {
            // Prepare data array
            $data = [
                'name' => $name,
                'description' => $description,
                'short_description' => $short_description,
                'sku' => $sku,
                'price' => $price,
                'sale_price' => $sale_price,
                'stock_quantity' => $stock_quantity,
                'category_id' => $category_id,
                'weight' => $weight,
                'dimensions' => $dimensions,
                'is_featured' => $is_featured,
                'meta_title' => $meta_title,
                'meta_description' => $meta_description
            ];

            // Update product
            try {
                $update_result = $product_class->updateProduct($product_id, $data);
                if ($update_result) {
                
                // Handle new image uploads
                if (!empty($_FILES['new_images']['name'][0])) {
                    $upload_dir = PRODUCT_IMAGES_PATH;
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    foreach ($_FILES['new_images']['tmp_name'] as $key => $tmp_name) {
                        if (!empty($tmp_name)) {
                            $file_info = array(
                                'name' => $_FILES['new_images']['name'][$key],
                                'tmp_name' => $tmp_name,
                                'size' => $_FILES['new_images']['size'][$key],
                                'error' => $_FILES['new_images']['error'][$key]
                            );
                            
                            $uploaded_filename = upload_image($file_info, $upload_dir);
                            if ($uploaded_filename) {
                                $product_class->addProductImage($product_id, $uploaded_filename, $name, false);
                            }
                        }
                    }
                }
                
                $success_message = "تم تحديث المنتج بنجاح!";
                // Refresh product data
                $product = $product_class->getProductById($product_id);
                $product_images = $product_class->getProductImages($product_id);
            } else {
                $error_message = "حدث خطأ أثناء تحديث المنتج";
            }
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
        }
    }
    
    // Handle image deletion
    if (isset($_POST['delete_image'])) {
        $image_id = (int)$_POST['image_id'];
        if ($product_class->deleteProductImage($image_id)) {
            $success_message = "تم حذف الصورة بنجاح!";
            $product_images = $product_class->getProductImages($product_id);
        } else {
            $error_message = "حدث خطأ أثناء حذف الصورة";
        }
    }
    
    // Handle setting primary image
    if (isset($_POST['set_primary'])) {
        $image_id = (int)$_POST['image_id'];
        if ($product_class->setPrimaryImage($product_id, $image_id)) {
            $success_message = "تم تحديد الصورة الرئيسية بنجاح!";
            $product_images = $product_class->getProductImages($product_id);
        } else {
            $error_message = "حدث خطأ أثناء تحديد الصورة الرئيسية";
        }
    }
}

$page_title = "تعديل المنتج: " . $product['name'];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    <!-- Admin RTL CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .form-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .image-upload-area:hover,
        .image-upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
        }
        
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .preview-item {
            position: relative;
            width: 150px;
            height: 150px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #dee2e6;
        }
        
        .image-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(220, 53, 69, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
        }
        
        .existing-image {
            position: relative;
            width: 150px;
            height: 150px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #dee2e6;
            margin-bottom: 1rem;
        }
        
        .existing-image.primary {
            border-color: #198754;
        }
        
        .primary-badge {
            position: absolute;
            top: 5px;
            left: 5px;
            background: #198754;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }
        
        .image-actions {
            position: absolute;
            bottom: 5px;
            left: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }
        
        .image-actions .btn {
            padding: 2px 6px;
            font-size: 10px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <i class="fas fa-shopping-bag me-2"></i>
                لوحة تحكم <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../../auth/logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2">
                <div class="list-group">
                    <a href="../index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> الرئيسية
                    </a>
                    <a href="index.php" class="list-group-item list-group-item-action active">
                        <i class="fas fa-box me-2"></i> المنتجات
                    </a>
                    <a href="../categories/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2"></i> التصنيفات
                    </a>
                    <a href="../orders/index.php" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> الطلبات
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج: <?php echo $product['name']; ?>
                    </h2>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للمنتجات
                    </a>
                </div>

                <!-- Messages -->
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Edit Product Form -->
                <form method="POST" enctype="multipart/form-data">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($product['name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sku" class="form-label">رقم المنتج (SKU)</label>
                                    <input type="text" class="form-control" id="sku" name="sku" 
                                           value="<?php echo htmlspecialchars($product['sku']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">التصنيف *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر التصنيف</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?php echo $cat['id']; ?>"
                                                    <?php echo $cat['id'] == $product['category_id'] ? 'selected' : ''; ?>>
                                                <?php echo $cat['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="price" class="form-label">السعر *</label>
                                    <input type="number" class="form-control" id="price" name="price"
                                           step="0.01" min="0" value="<?php echo $product['price']; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sale_price" class="form-label">سعر التخفيض</label>
                                    <input type="number" class="form-control" id="sale_price" name="sale_price"
                                           step="0.01" min="0" value="<?php echo $product['sale_price']; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="stock_quantity" class="form-label">الكمية المتوفرة *</label>
                                    <input type="number" class="form-control" id="stock_quantity" name="stock_quantity"
                                           min="0" value="<?php echo $product['stock_quantity']; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">الوزن (كجم)</label>
                                    <input type="number" class="form-control" id="weight" name="weight"
                                           step="0.01" min="0" value="<?php echo $product['weight']; ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="dimensions" class="form-label">الأبعاد</label>
                                    <input type="text" class="form-control" id="dimensions" name="dimensions"
                                           value="<?php echo htmlspecialchars($product['dimensions']); ?>"
                                           placeholder="الطول × العرض × الارتفاع">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                       <?php echo $product['is_featured'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_featured">
                                    منتج مميز
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">الوصف المختصر</label>
                            <textarea class="form-control" id="short_description" name="short_description"
                                      rows="3" maxlength="500"><?php echo htmlspecialchars($product['short_description']); ?></textarea>
                            <div class="form-text">الحد الأقصى 500 حرف</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف التفصيلي</label>
                            <textarea class="form-control" id="description" name="description"
                                      rows="6"><?php echo htmlspecialchars($product['description']); ?></textarea>
                        </div>
                    </div>

                    <!-- SEO Settings -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-search me-2"></i>
                            إعدادات محركات البحث (SEO)
                        </h5>

                        <div class="mb-3">
                            <label for="meta_title" class="form-label">عنوان الصفحة</label>
                            <input type="text" class="form-control" id="meta_title" name="meta_title"
                                   value="<?php echo htmlspecialchars($product['meta_title']); ?>" maxlength="200">
                            <div class="form-text">الحد الأقصى 200 حرف</div>
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">وصف الصفحة</label>
                            <textarea class="form-control" id="meta_description" name="meta_description"
                                      rows="3" maxlength="300"><?php echo htmlspecialchars($product['meta_description']); ?></textarea>
                            <div class="form-text">الحد الأقصى 300 حرف</div>
                        </div>
                    </div>

                    <!-- Existing Images -->
                    <?php if (!empty($product_images)): ?>
                        <div class="form-section">
                            <h5 class="mb-4">
                                <i class="fas fa-images me-2"></i>
                                الصور الحالية
                            </h5>

                            <div class="row">
                                <?php foreach ($product_images as $image): ?>
                                    <div class="col-md-3 mb-3">
                                        <div class="existing-image <?php echo $image['is_primary'] ? 'primary' : ''; ?>">
                                            <?php echo generate_product_image_html(
                                                $image['image_path'],
                                                $product['name'],
                                                'image-preview'
                                            ); ?>

                                            <?php if ($image['is_primary']): ?>
                                                <span class="primary-badge">رئيسية</span>
                                            <?php endif; ?>

                                            <div class="image-actions">
                                                <?php if (!$image['is_primary']): ?>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="image_id" value="<?php echo $image['id']; ?>">
                                                        <button type="submit" name="set_primary" class="btn btn-success btn-sm"
                                                                onclick="return confirm('هل تريد جعل هذه الصورة رئيسية؟')">
                                                            رئيسية
                                                        </button>
                                                    </form>
                                                <?php endif; ?>

                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="image_id" value="<?php echo $image['id']; ?>">
                                                    <button type="submit" name="delete_image" class="btn btn-danger btn-sm"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذه الصورة؟')">
                                                        حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Add New Images -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-plus me-2"></i>
                            إضافة صور جديدة
                        </h5>

                        <div class="image-upload-area" onclick="document.getElementById('new_images').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h6>اضغط لرفع الصور أو اسحبها هنا</h6>
                            <p class="text-muted mb-0">يمكنك رفع عدة صور (JPG, PNG, GIF, WEBP)</p>
                            <input type="file" id="new_images" name="new_images[]" multiple accept="image/*" class="d-none">
                        </div>

                        <div class="preview-container" id="newImagePreview"></div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-section">
                        <div class="d-flex gap-3">
                            <button type="submit" name="update_product" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="index.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // New image upload handling
        const newImageInput = document.getElementById('new_images');
        const newImagePreview = document.getElementById('newImagePreview');
        const uploadArea = document.querySelector('.image-upload-area');

        newImageInput.addEventListener('change', handleNewImageSelect);

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            newImageInput.files = files;
            handleNewImageSelect();
        });

        function handleNewImageSelect() {
            const files = newImageInput.files;
            newImagePreview.innerHTML = '';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" class="image-preview" alt="Preview">
                            <button type="button" class="preview-remove" onclick="removeNewImage(this, ${i})">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        newImagePreview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }

        function removeNewImage(button, index) {
            const dt = new DataTransfer();
            const files = newImageInput.files;

            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }

            newImageInput.files = dt.files;
            button.parentElement.remove();
        }
    </script>
</body>
</html>
