<?php
/**
 * Setup Product Variants Tables - إعداد جداول متغيرات المنتجات
 */

require_once 'config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "<h1>إعداد جداول متغيرات المنتجات</h1>";

    // Read SQL file
    $sql_file = 'database/create_variants_tables.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }

    $sql = file_get_contents($sql_file);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    $success_count = 0;
    $error_count = 0;

    foreach ($statements as $statement) {
        try {
            $db->exec($statement);
            $success_count++;
            
            // Extract table name for display
            if (preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches)) {
                echo "<p style='color: green;'>✅ تم إنشاء جدول: {$matches[1]}</p>";
            } elseif (preg_match('/INSERT INTO.*?`([^`]+)`/i', $statement, $matches)) {
                echo "<p style='color: blue;'>📝 تم إدراج بيانات في جدول: {$matches[1]}</p>";
            } else {
                echo "<p style='color: green;'>✅ تم تنفيذ استعلام بنجاح</p>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
            echo "<details><summary>الاستعلام</summary><pre>" . htmlspecialchars($statement) . "</pre></details>";
        }
    }

    echo "<hr>";
    echo "<h2>ملخص النتائج</h2>";
    echo "<p><strong>الاستعلامات الناجحة:</strong> $success_count</p>";
    echo "<p><strong>الاستعلامات الفاشلة:</strong> $error_count</p>";

    if ($error_count === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم إعداد جداول متغيرات المنتجات بنجاح!</h3>";
        echo "<p>يمكنك الآن:</p>";
        echo "<ul>";
        echo "<li><a href='admin/products/add.php'>إضافة منتج جديد بمتغيرات</a></li>";
        echo "<li><a href='admin/products/'>إدارة المنتجات</a></li>";
        echo "<li><a href='test_variants.php'>اختبار نظام المتغيرات</a></li>";
        echo "</ul>";
        echo "</div>";
    }

    // Verify tables were created
    echo "<h2>التحقق من الجداول</h2>";
    $tables = ['product_attributes', 'product_attribute_values', 'product_variants', 'product_variant_attributes'];
    
    foreach ($tables as $table) {
        try {
            $query = "SELECT COUNT(*) as count FROM $table";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p>✅ جدول <strong>$table</strong>: $count سجل</p>";
        } catch (PDOException $e) {
            echo "<p>❌ جدول <strong>$table</strong>: غير موجود</p>";
        }
    }

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ حدث خطأ</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2 {
    color: #2c3e50;
}

details {
    margin: 10px 0;
    padding: 10px;
    background: #f1f1f1;
    border-radius: 5px;
}

pre {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
