-- Create Product Variants Tables
-- إنشاء جداول متغيرات المنتجات

-- 1. Product Attributes Table (خصائص المنتجات)
CREATE TABLE IF NOT EXISTS `product_attributes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL COMMENT 'اسم الخاصية بالإنجليزية',
    `display_name` varchar(100) NOT NULL COMMENT 'اسم الخاصية للعرض',
    `type` enum('select','color','text','number') DEFAULT 'select' COMMENT 'نوع الخاصية',
    `is_required` tinyint(1) DEFAULT 0 COMMENT 'هل الخاصية مطلوبة',
    `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
    `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`),
    KEY `is_active` (`is_active`),
    KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول خصائص المنتجات';

-- 2. Product Attribute Values Table (قيم خصائص المنتجات)
CREATE TABLE IF NOT EXISTS `product_attribute_values` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `attribute_id` int(11) NOT NULL COMMENT 'معرف الخاصية',
    `value` varchar(100) NOT NULL COMMENT 'القيمة',
    `display_value` varchar(100) NOT NULL COMMENT 'القيمة للعرض',
    `color_code` varchar(7) DEFAULT NULL COMMENT 'كود اللون (للألوان)',
    `image` varchar(255) DEFAULT NULL COMMENT 'صورة القيمة',
    `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
    `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `attribute_id` (`attribute_id`),
    KEY `is_active` (`is_active`),
    KEY `sort_order` (`sort_order`),
    UNIQUE KEY `unique_attribute_value` (`attribute_id`, `value`),
    CONSTRAINT `fk_attribute_values_attribute` FOREIGN KEY (`attribute_id`) REFERENCES `product_attributes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول قيم خصائص المنتجات';

-- 3. Product Variants Table (متغيرات المنتجات)
CREATE TABLE IF NOT EXISTS `product_variants` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
    `sku` varchar(100) DEFAULT NULL COMMENT 'رمز المنتج الفريد',
    `price` decimal(10,2) DEFAULT NULL COMMENT 'سعر المتغير (اختياري)',
    `sale_price` decimal(10,2) DEFAULT NULL COMMENT 'سعر التخفيض',
    `stock_quantity` int(11) DEFAULT 0 COMMENT 'كمية المخزون',
    `weight` decimal(8,2) DEFAULT NULL COMMENT 'الوزن',
    `dimensions` varchar(100) DEFAULT NULL COMMENT 'الأبعاد',
    `image` varchar(255) DEFAULT NULL COMMENT 'صورة المتغير',
    `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `product_id` (`product_id`),
    KEY `sku` (`sku`),
    KEY `is_active` (`is_active`),
    KEY `stock_quantity` (`stock_quantity`),
    CONSTRAINT `fk_variants_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول متغيرات المنتجات';

-- 4. Product Variant Attributes Table (ربط متغيرات المنتجات بالخصائص)
CREATE TABLE IF NOT EXISTS `product_variant_attributes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `variant_id` int(11) NOT NULL COMMENT 'معرف المتغير',
    `attribute_value_id` int(11) NOT NULL COMMENT 'معرف قيمة الخاصية',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `variant_id` (`variant_id`),
    KEY `attribute_value_id` (`attribute_value_id`),
    UNIQUE KEY `unique_variant_attribute` (`variant_id`, `attribute_value_id`),
    CONSTRAINT `fk_variant_attributes_variant` FOREIGN KEY (`variant_id`) REFERENCES `product_variants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_variant_attributes_value` FOREIGN KEY (`attribute_value_id`) REFERENCES `product_attribute_values` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول ربط متغيرات المنتجات بالخصائص';

-- Insert Sample Attributes
INSERT INTO `product_attributes` (`name`, `display_name`, `type`, `sort_order`) VALUES
('size', 'المقاس', 'select', 1),
('color', 'اللون', 'color', 2),
('material', 'المادة', 'select', 3),
('storage', 'التخزين', 'select', 4);

-- Insert Sample Attribute Values
-- Sizes
INSERT INTO `product_attribute_values` (`attribute_id`, `value`, `display_value`, `sort_order`) VALUES
((SELECT id FROM product_attributes WHERE name = 'size'), 'XS', 'صغير جداً', 1),
((SELECT id FROM product_attributes WHERE name = 'size'), 'S', 'صغير', 2),
((SELECT id FROM product_attributes WHERE name = 'size'), 'M', 'متوسط', 3),
((SELECT id FROM product_attributes WHERE name = 'size'), 'L', 'كبير', 4),
((SELECT id FROM product_attributes WHERE name = 'size'), 'XL', 'كبير جداً', 5),
((SELECT id FROM product_attributes WHERE name = 'size'), 'XXL', 'كبير جداً جداً', 6);

-- Colors
INSERT INTO `product_attribute_values` (`attribute_id`, `value`, `display_value`, `color_code`, `sort_order`) VALUES
((SELECT id FROM product_attributes WHERE name = 'color'), 'red', 'أحمر', '#FF0000', 1),
((SELECT id FROM product_attributes WHERE name = 'color'), 'blue', 'أزرق', '#0000FF', 2),
((SELECT id FROM product_attributes WHERE name = 'color'), 'green', 'أخضر', '#00FF00', 3),
((SELECT id FROM product_attributes WHERE name = 'color'), 'black', 'أسود', '#000000', 4),
((SELECT id FROM product_attributes WHERE name = 'color'), 'white', 'أبيض', '#FFFFFF', 5),
((SELECT id FROM product_attributes WHERE name = 'color'), 'yellow', 'أصفر', '#FFFF00', 6),
((SELECT id FROM product_attributes WHERE name = 'color'), 'pink', 'وردي', '#FFC0CB', 7),
((SELECT id FROM product_attributes WHERE name = 'color'), 'purple', 'بنفسجي', '#800080', 8);

-- Materials
INSERT INTO `product_attribute_values` (`attribute_id`, `value`, `display_value`, `sort_order`) VALUES
((SELECT id FROM product_attributes WHERE name = 'material'), 'cotton', 'قطن', 1),
((SELECT id FROM product_attributes WHERE name = 'material'), 'polyester', 'بوليستر', 2),
((SELECT id FROM product_attributes WHERE name = 'material'), 'leather', 'جلد', 3),
((SELECT id FROM product_attributes WHERE name = 'material'), 'silk', 'حرير', 4),
((SELECT id FROM product_attributes WHERE name = 'material'), 'wool', 'صوف', 5),
((SELECT id FROM product_attributes WHERE name = 'material'), 'denim', 'جينز', 6);

-- Storage
INSERT INTO `product_attribute_values` (`attribute_id`, `value`, `display_value`, `sort_order`) VALUES
((SELECT id FROM product_attributes WHERE name = 'storage'), '64GB', '64 جيجابايت', 1),
((SELECT id FROM product_attributes WHERE name = 'storage'), '128GB', '128 جيجابايت', 2),
((SELECT id FROM product_attributes WHERE name = 'storage'), '256GB', '256 جيجابايت', 3),
((SELECT id FROM product_attributes WHERE name = 'storage'), '512GB', '512 جيجابايت', 4),
((SELECT id FROM product_attributes WHERE name = 'storage'), '1TB', '1 تيرابايت', 5);
