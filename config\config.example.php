<?php
/**
 * Configuration Example File - ملف الإعدادات النموذجي
 * Copy this file to config.php and update the values
 * انسخ هذا الملف إلى config.php وحدث القيم
 */

// Database Configuration - إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'shoppy_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration - إعدادات الموقع
define('SITE_NAME', 'متجر شوبي');
define('SITE_DESCRIPTION', 'متجر إلكتروني متكامل للتسوق الآمن والسريع');
define('SITE_KEYWORDS', 'تسوق, متجر إلكتروني, منتجات, عروض');
define('SITE_AUTHOR', 'Shoppy Team');

// URL Configuration - إعدادات الروابط
define('BASE_URL', 'http://localhost/shoppy/');
define('ASSETS_URL', BASE_URL . 'assets/');
define('ADMIN_URL', BASE_URL . 'admin/');

// File Paths - مسارات الملفات
define('ROOT_PATH', dirname(__DIR__) . '/');
define('UPLOAD_PATH', ROOT_PATH . 'assets/images/');
define('PRODUCT_IMAGES_PATH', UPLOAD_PATH . 'products/');
define('CATEGORY_IMAGES_PATH', UPLOAD_PATH . 'categories/');

// Pagination - ترقيم الصفحات
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);
define('USERS_PER_PAGE', 20);

// Security - الأمان
define('SESSION_LIFETIME', 3600); // 1 hour
define('REMEMBER_ME_LIFETIME', 2592000); // 30 days
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Email Configuration - إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'متجر شوبي');

// Telegram Notifications - إشعارات تليجرام
define('TELEGRAM_BOT_TOKEN', 'your_bot_token');
define('TELEGRAM_CHAT_ID', 'your_chat_id');

// Payment Gateway - بوابة الدفع
define('PAYMENT_GATEWAY', 'cod'); // cod, paypal, stripe
define('PAYPAL_CLIENT_ID', 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', 'your_paypal_client_secret');
define('PAYPAL_SANDBOX', true); // Set to false for production

// File Upload Limits - حدود رفع الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Cache Configuration - إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour

// Debug Mode - وضع التطوير
define('DEBUG_MODE', true); // Set to false in production
define('LOG_ERRORS', true);
define('ERROR_LOG_PATH', ROOT_PATH . 'logs/error.log');

// Timezone - المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// Language - اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Currency - العملة
define('DEFAULT_CURRENCY', 'SAR');
define('CURRENCY_SYMBOL', 'ر.س');

// Social Media Links - روابط وسائل التواصل
define('FACEBOOK_URL', 'https://facebook.com/shoppy');
define('TWITTER_URL', 'https://twitter.com/shoppy');
define('INSTAGRAM_URL', 'https://instagram.com/shoppy');
define('WHATSAPP_NUMBER', '+966501234567');

// Business Information - معلومات العمل
define('BUSINESS_NAME', 'شركة شوبي للتجارة الإلكترونية');
define('BUSINESS_ADDRESS', 'الرياض، المملكة العربية السعودية');
define('BUSINESS_PHONE', '+966112345678');
define('BUSINESS_EMAIL', '<EMAIL>');
define('BUSINESS_CR', '1234567890'); // Commercial Registration

// Shipping Configuration - إعدادات الشحن
define('FREE_SHIPPING_THRESHOLD', 200.00); // Free shipping for orders above this amount
define('DEFAULT_SHIPPING_COST', 25.00);
define('EXPRESS_SHIPPING_COST', 50.00);
define('SHIPPING_ZONES', [
    'riyadh' => ['name' => 'الرياض', 'cost' => 25.00],
    'jeddah' => ['name' => 'جدة', 'cost' => 30.00],
    'dammam' => ['name' => 'الدمام', 'cost' => 35.00],
    'other' => ['name' => 'مدن أخرى', 'cost' => 40.00]
]);

// Tax Configuration - إعدادات الضريبة
define('TAX_RATE', 0.15); // 15% VAT
define('TAX_INCLUDED_IN_PRICE', false);

// Inventory Management - إدارة المخزون
define('LOW_STOCK_THRESHOLD', 5);
define('OUT_OF_STOCK_BEHAVIOR', 'hide'); // hide, show, backorder

// SEO Configuration - إعدادات تحسين محركات البحث
define('SEO_TITLE_SUFFIX', ' - متجر شوبي');
define('SEO_DEFAULT_DESCRIPTION', 'متجر شوبي - أفضل متجر إلكتروني للتسوق الآمن والسريع');
define('SEO_DEFAULT_KEYWORDS', 'تسوق, متجر إلكتروني, منتجات, عروض, شوبي');

// Analytics - التحليلات
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');
define('FACEBOOK_PIXEL_ID', 'FACEBOOK_PIXEL_ID');

// API Keys - مفاتيح واجهات البرمجة
define('GOOGLE_MAPS_API_KEY', 'your_google_maps_api_key');
define('RECAPTCHA_SITE_KEY', 'your_recaptcha_site_key');
define('RECAPTCHA_SECRET_KEY', 'your_recaptcha_secret_key');

// Backup Configuration - إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', ROOT_PATH . 'backups/');
define('BACKUP_RETENTION_DAYS', 30);

// Maintenance Mode - وضع الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'الموقع تحت الصيانة. سنعود قريباً!');
define('MAINTENANCE_ALLOWED_IPS', ['127.0.0.1', '::1']);

// Error Reporting - تقارير الأخطاء
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Session Configuration - إعدادات الجلسة
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS
ini_set('session.use_strict_mode', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize error logging
if (LOG_ERRORS) {
    $log_dir = dirname(ERROR_LOG_PATH);
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    ini_set('log_errors', 1);
    ini_set('error_log', ERROR_LOG_PATH);
}

// Autoload classes
spl_autoload_register(function ($class_name) {
    $class_file = ROOT_PATH . 'classes/' . $class_name . '.php';
    if (file_exists($class_file)) {
        require_once $class_file;
    }
});

// Set default timezone
date_default_timezone_set('Asia/Riyadh');

// Maintenance mode check
if (MAINTENANCE_MODE && !in_array($_SERVER['REMOTE_ADDR'], MAINTENANCE_ALLOWED_IPS)) {
    http_response_code(503);
    echo '<!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>صيانة - ' . SITE_NAME . '</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .maintenance { max-width: 500px; margin: 0 auto; }
            .icon { font-size: 4rem; color: #ffc107; margin-bottom: 1rem; }
        </style>
    </head>
    <body>
        <div class="maintenance">
            <div class="icon">🔧</div>
            <h1>الموقع تحت الصيانة</h1>
            <p>' . MAINTENANCE_MESSAGE . '</p>
        </div>
    </body>
    </html>';
    exit();
}
?>
