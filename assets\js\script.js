/**
 * Shoppy E-Commerce Custom JavaScript
 * سكريبت متجر شوبي المخصص
 */

// Global variables
let cartCount = 0;

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize application
 * تهيئة التطبيق
 */
function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize cart functionality
    initializeCart();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize image lazy loading
    initializeLazyLoading();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Update cart count
    updateCartCount();
}

/**
 * Initialize Bootstrap tooltips
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize cart functionality
 * تهيئة وظائف السلة
 */
function initializeCart() {
    // Add to cart buttons
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.dataset.productId;
            const variantId = this.dataset.variantId || null;
            const quantity = this.dataset.quantity || 1;
            
            addToCart(productId, variantId, quantity);
        });
    });
    
    // Quantity change handlers
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            updateCartItemQuantity(this.dataset.cartKey, this.value);
        });
    });
    
    // Remove from cart buttons
    document.querySelectorAll('.remove-from-cart').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const cartKey = this.dataset.cartKey;
            removeFromCart(cartKey);
        });
    });
}

/**
 * Add product to cart
 * إضافة منتج للسلة
 */
function addToCart(productId, variantId = null, quantity = 1) {
    const formData = new FormData();
    formData.append('action', 'add_to_cart');
    formData.append('product_id', productId);
    formData.append('quantity', quantity);
    
    if (variantId) {
        formData.append('variant_id', variantId);
    }
    
    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة المنتج إلى السلة!', 'success');
            updateCartCount();
            
            // Update button state
            const button = document.querySelector(`[data-product-id="${productId}"]`);
            if (button) {
                button.innerHTML = '<i class="fas fa-check me-2"></i>تم الإضافة';
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
                
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-cart-plus me-2"></i>أضف إلى السلة';
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                }, 2000);
            }
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

/**
 * Remove product from cart
 * إزالة منتج من السلة
 */
function removeFromCart(cartKey) {
    if (!confirm('هل تريد إزالة هذا المنتج من السلة؟')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'remove_from_cart');
    formData.append('cart_key', cartKey);
    
    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إزالة المنتج من السلة', 'success');
            updateCartCount();
            
            // Remove item from DOM
            const cartItem = document.querySelector(`[data-cart-key="${cartKey}"]`);
            if (cartItem) {
                cartItem.remove();
            }
            
            // Reload page if on cart page
            if (window.location.pathname.includes('cart.php')) {
                location.reload();
            }
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إزالة المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

/**
 * Update cart item quantity
 * تحديث كمية المنتج في السلة
 */
function updateCartItemQuantity(cartKey, quantity) {
    const formData = new FormData();
    formData.append('action', 'update_quantity');
    formData.append('cart_key', cartKey);
    formData.append('quantity', quantity);
    
    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount();
            updateCartTotals();
        } else {
            showNotification(data.message || 'حدث خطأ أثناء تحديث الكمية', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

/**
 * Update cart count in navigation
 * تحديث عدد المنتجات في السلة
 */
function updateCartCount() {
    fetch('api/cart.php?action=get_count')
    .then(response => response.json())
    .then(data => {
        const cartBadges = document.querySelectorAll('.cart-badge');
        cartBadges.forEach(badge => {
            if (data.count > 0) {
                badge.textContent = data.count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        });
        cartCount = data.count;
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

/**
 * Update cart totals
 * تحديث إجماليات السلة
 */
function updateCartTotals() {
    fetch('api/cart.php?action=get_totals')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update totals in cart page
            const subtotalElement = document.getElementById('subtotal');
            const totalElement = document.getElementById('total');
            
            if (subtotalElement) {
                subtotalElement.textContent = data.subtotal_formatted;
            }
            
            if (totalElement) {
                totalElement.textContent = data.total_formatted;
            }
        }
    })
    .catch(error => {
        console.error('Error updating cart totals:', error);
    });
}

/**
 * Initialize search functionality
 * تهيئة وظائف البحث
 */
function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value);
            }, 500);
        });
    }
}

/**
 * Perform search with suggestions
 * تنفيذ البحث مع الاقتراحات
 */
function performSearch(query) {
    if (query.length < 2) return;
    
    fetch(`api/search.php?q=${encodeURIComponent(query)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.suggestions.length > 0) {
            showSearchSuggestions(data.suggestions);
        }
    })
    .catch(error => {
        console.error('Search error:', error);
    });
}

/**
 * Show search suggestions
 * عرض اقتراحات البحث
 */
function showSearchSuggestions(suggestions) {
    // Implementation for search suggestions dropdown
    console.log('Search suggestions:', suggestions);
}

/**
 * Initialize lazy loading for images
 * تهيئة التحميل التدريجي للصور
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Initialize smooth scrolling
 * تهيئة التمرير السلس
 */
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Initialize form validation
 * تهيئة التحقق من النماذج
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * Show notification
 * عرض إشعار
 */
function showNotification(message, type = 'info', duration = 5000) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-triangle',
        'warning': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    };
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="${icon[type]} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

/**
 * Format price
 * تنسيق السعر
 */
function formatPrice(price, currency = 'SAR') {
    const currencies = {
        'SAR': { locale: 'ar-SA', symbol: 'ر.س' },
        'EGP': { locale: 'ar-EG', symbol: 'ج.م' },
        'USD': { locale: 'en-US', symbol: '$' },
        'EUR': { locale: 'de-DE', symbol: '€' }
    };

    const currencyInfo = currencies[currency] || currencies['SAR'];

    try {
        return new Intl.NumberFormat(currencyInfo.locale, {
            style: 'currency',
            currency: currency
        }).format(price);
    } catch (e) {
        // Fallback if currency is not supported
        return new Intl.NumberFormat('ar-SA').format(price) + ' ' + currencyInfo.symbol;
    }
}

/**
 * Debounce function
 * دالة التأخير
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Loading state management
 * إدارة حالة التحميل
 */
function showLoading(element) {
    element.classList.add('loading');
    element.disabled = true;
}

function hideLoading(element) {
    element.classList.remove('loading');
    element.disabled = false;
}

/**
 * Image upload preview
 * معاينة رفع الصور
 */
function previewImages(input, previewContainer) {
    const files = input.files;
    previewContainer.innerHTML = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'preview-item position-relative d-inline-block m-2';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                    <button type="button" class="btn btn-danger btn-sm position-absolute top-0 start-100 translate-middle rounded-circle" 
                            onclick="removePreview(this)" style="width: 25px; height: 25px; padding: 0;">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        }
    }
}

/**
 * Remove image preview
 * إزالة معاينة الصورة
 */
function removePreview(button) {
    button.parentElement.remove();
}

/**
 * Validate form fields
 * التحقق من حقول النموذج
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * Phone number formatting
 * تنسيق رقم الهاتف
 */
function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    
    if (value.length > 0) {
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('05')) {
            value = '+966' + value.substring(1);
        } else if (value.startsWith('5')) {
            value = '+966' + value;
        }
    }
    
    input.value = value;
}

/**
 * Copy to clipboard
 * نسخ إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('تم النسخ إلى الحافظة', 'success');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        showNotification('فشل في النسخ', 'error');
    });
}

/**
 * Confirm action
 * تأكيد الإجراء
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Auto-save form data
 * حفظ بيانات النموذج تلقائياً
 */
function autoSaveForm(form, key) {
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    localStorage.setItem(`autosave_${key}`, JSON.stringify(data));
}

/**
 * Restore form data
 * استعادة بيانات النموذج
 */
function restoreFormData(form, key) {
    const savedData = localStorage.getItem(`autosave_${key}`);
    
    if (savedData) {
        const data = JSON.parse(savedData);
        
        Object.keys(data).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.value = data[fieldName];
            }
        });
    }
}

/**
 * Clear auto-saved data
 * مسح البيانات المحفوظة تلقائياً
 */
function clearAutoSavedData(key) {
    localStorage.removeItem(`autosave_${key}`);
}

/**
 * Initialize product image gallery
 * تهيئة معرض صور المنتج
 */
function initializeImageGallery() {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.product-thumbnail');
    
    if (mainImage && thumbnails.length > 0) {
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                mainImage.src = this.src;
                
                // Update active thumbnail
                thumbnails.forEach(thumb => thumb.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Image zoom functionality
        mainImage.addEventListener('click', function() {
            openImageZoom(this.src);
        });
    }
}

/**
 * Open image zoom modal
 * فتح نافذة تكبير الصورة
 */
function openImageZoom(imageSrc) {
    const zoomOverlay = document.getElementById('zoomOverlay');
    const zoomImage = document.getElementById('zoomImage');
    
    if (zoomOverlay && zoomImage) {
        zoomImage.src = imageSrc;
        zoomOverlay.style.display = 'flex';
    }
}

/**
 * Close image zoom modal
 * إغلاق نافذة تكبير الصورة
 */
function closeImageZoom() {
    const zoomOverlay = document.getElementById('zoomOverlay');
    if (zoomOverlay) {
        zoomOverlay.style.display = 'none';
    }
}

/**
 * Initialize product variants
 * تهيئة متغيرات المنتج
 */
function initializeProductVariants() {
    const variantOptions = document.querySelectorAll('.variant-option');
    
    variantOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from all options
            variantOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add active class to selected option
            this.classList.add('selected');
            
            // Update hidden input
            const selectedVariantInput = document.getElementById('selectedVariant');
            if (selectedVariantInput) {
                selectedVariantInput.value = this.dataset.variantId;
            }
            
            // Update price if variant has different price
            const variantPrice = this.dataset.price;
            if (variantPrice) {
                const priceElement = document.querySelector('.product-price');
                if (priceElement) {
                    priceElement.textContent = formatPrice(variantPrice);
                }
            }
            
            // Update stock quantity
            const stockQuantity = this.dataset.stock;
            const quantityInput = document.getElementById('quantity');
            if (quantityInput && stockQuantity) {
                quantityInput.max = stockQuantity;
                if (parseInt(quantityInput.value) > parseInt(stockQuantity)) {
                    quantityInput.value = stockQuantity;
                }
            }
        });
    });
}

/**
 * Initialize admin dashboard
 * تهيئة لوحة تحكم الإدارة
 */
function initializeAdminDashboard() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('adminSidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (sidebarToggle && sidebar && mainContent) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
                mainContent.classList.remove('sidebar-open');
            }
        });
    }
}

/**
 * Initialize page-specific functionality
 * تهيئة الوظائف الخاصة بالصفحة
 */
function initializePageSpecific() {
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('product.php')) {
        initializeImageGallery();
        initializeProductVariants();
    }
    
    if (currentPage.includes('admin/')) {
        initializeAdminDashboard();
    }
}

// Initialize page-specific functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePageSpecific);

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    // You can send error reports to server here
});

// Service Worker registration (for PWA features)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
        .then(registration => {
            console.log('SW registered: ', registration);
        })
        .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
        });
    });
}
