<?php
/**
 * Professional Responsive Navigation Bar - شريط التنقل الاحترافي المتجاوب
 */

// Get current page for active states
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_category = $_GET['category'] ?? '';
?>

<!-- Enhanced Navbar CSS -->
<link href="<?php echo BASE_URL; ?>assets/css/navbar-enhanced.css" rel="stylesheet">

<!-- Professional Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark professional-navbar sticky-top">
    <div class="container-fluid px-lg-5">
        <!-- Brand -->
        <a class="navbar-brand professional-brand" href="<?php echo BASE_URL; ?>">
            <div class="brand-container">
                <div class="brand-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name"><?php echo SITE_NAME; ?></span>
                    <span class="brand-tagline">متجرك الإلكتروني</span>
                </div>
            </div>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler professional-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span></span>
            <span></span>
            <span></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Main Navigation -->
            <ul class="navbar-nav professional-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link professional-link <?php echo $current_page == 'index' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>">
                        <span class="nav-icon">
                            <i class="fas fa-home"></i>
                        </span>
                        <span class="nav-text">الرئيسية</span>
                    </a>
                </li>

                <!-- Categories Mega Menu -->
                <li class="nav-item dropdown mega-dropdown">
                    <a class="nav-link professional-link dropdown-toggle <?php echo $current_category ? 'active' : ''; ?>"
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="nav-icon">
                            <i class="fas fa-th-large"></i>
                        </span>
                        <span class="nav-text">التصنيفات</span>
                    </a>
                    <div class="dropdown-menu mega-menu" data-bs-auto-close="outside">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mega-menu-header">
                                        <h6 class="mega-menu-title">
                                            <i class="fas fa-tags me-2"></i>
                                            تصنيفات المنتجات
                                        </h6>
                                    </div>
                                    <div class="mega-menu-content">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <a class="mega-menu-item" href="<?php echo BASE_URL; ?>">
                                                    <div class="mega-item-icon">
                                                        <i class="fas fa-th-large"></i>
                                                    </div>
                                                    <div class="mega-item-content">
                                                        <h6>جميع المنتجات</h6>
                                                        <p>تصفح جميع المنتجات المتاحة</p>
                                                    </div>
                                                </a>
                                            </div>
                                            <?php
                                            // Get categories for dropdown
                                            if (isset($category) && $category instanceof Category) {
                                                $nav_categories = $category->getAllCategories();
                                            } else {
                                                require_once 'classes/Category.php';
                                                $database = new Database();
                                                $db = $database->getConnection();
                                                $nav_category = new Category($db);
                                                $nav_categories = $nav_category->getAllCategories();
                                            }

                                            $category_count = 0;
                                            foreach ($nav_categories as $cat):
                                                if ($category_count >= 3) break; // Limit to 3 categories for better layout
                                            ?>
                                                <div class="col-md-3">
                                                    <a class="mega-menu-item <?php echo $current_category == $cat['id'] ? 'active' : ''; ?>"
                                                       href="<?php echo BASE_URL; ?>?category=<?php echo $cat['id']; ?>">
                                                        <div class="mega-item-icon">
                                                            <i class="fas fa-folder"></i>
                                                        </div>
                                                        <div class="mega-item-content">
                                                            <h6><?php echo $cat['name']; ?></h6>
                                                            <p><?php echo $cat['product_count']; ?> منتج</p>
                                                        </div>
                                                        <?php if ($cat['product_count'] > 0): ?>
                                                            <span class="mega-item-badge"><?php echo $cat['product_count']; ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </div>
                                            <?php
                                                $category_count++;
                                            endforeach;
                                            ?>
                                        </div>
                                        <?php if (count($nav_categories) > 3): ?>
                                            <div class="mega-menu-footer">
                                                <a href="<?php echo BASE_URL; ?>categories.php" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-plus me-1"></i>
                                                    عرض جميع التصنيفات
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>

                <!-- Offers Link -->
                <li class="nav-item">
                    <a class="nav-link professional-link offers-link" href="<?php echo BASE_URL; ?>?offers=1">
                        <span class="nav-icon">
                            <i class="fas fa-fire"></i>
                        </span>
                        <span class="nav-text">العروض</span>
                        <span class="offers-badge">جديد</span>
                    </a>
                </li>

                <!-- About Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link professional-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="nav-icon">
                            <i class="fas fa-info-circle"></i>
                        </span>
                        <span class="nav-text">حول الموقع</span>
                    </a>
                    <ul class="dropdown-menu professional-dropdown">
                        <li>
                            <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>about.php">
                                <i class="fas fa-users me-2"></i>
                                من نحن
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>contact.php">
                                <i class="fas fa-envelope me-2"></i>
                                اتصل بنا
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>privacy.php">
                                <i class="fas fa-shield-alt me-2"></i>
                                سياسة الخصوصية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>terms.php">
                                <i class="fas fa-file-contract me-2"></i>
                                الشروط والأحكام
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>

            <!-- Professional Search Form -->
            <div class="professional-search me-3">
                <form class="search-form" method="GET" action="<?php echo BASE_URL; ?>">
                    <div class="search-container">
                        <input class="search-input"
                               type="search"
                               name="search"
                               placeholder="البحث عن المنتجات..."
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                               autocomplete="off">
                        <button class="search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                </form>
            </div>

            <!-- Professional Right Side Navigation -->
            <ul class="navbar-nav professional-nav-right">
                <!-- Cart -->
                <li class="nav-item">
                    <a class="nav-link professional-link cart-link" href="<?php echo BASE_URL; ?>cart.php">
                        <span class="nav-icon cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <?php
                            $cart_count = get_cart_count();
                            if ($cart_count > 0):
                            ?>
                                <span class="cart-badge"><?php echo $cart_count; ?></span>
                            <?php endif; ?>
                        </span>
                        <span class="nav-text d-none d-lg-inline">السلة</span>
                    </a>
                </li>

                <!-- Wishlist -->
                <li class="nav-item">
                    <a class="nav-link professional-link wishlist-link" href="<?php echo BASE_URL; ?>user/wishlist.php">
                        <span class="nav-icon">
                            <i class="fas fa-heart"></i>
                        </span>
                        <span class="nav-text d-none d-lg-inline">المفضلة</span>
                    </a>
                </li>

                <!-- User Menu -->
                <?php if (is_logged_in()): ?>
                    <li class="nav-item dropdown user-dropdown">
                        <a class="nav-link professional-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="user-avatar">
                                <i class="fas fa-user"></i>
                            </span>
                            <span class="user-name d-none d-lg-inline"><?php echo $_SESSION['user_name']; ?></span>
                        </a>
                        <ul class="dropdown-menu professional-dropdown user-dropdown-menu dropdown-menu-end" data-bs-auto-close="true">
                            <li class="dropdown-header">
                                <div class="user-info">
                                    <div class="user-avatar-large">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-details">
                                        <h6><?php echo $_SESSION['user_name']; ?></h6>
                                        <small class="text-muted">عضو نشط</small>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>user/dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>user/orders.php">
                                    <i class="fas fa-shopping-bag me-2"></i>
                                    طلباتي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>user/profile.php">
                                    <i class="fas fa-user-edit me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item professional-dropdown-item" href="<?php echo BASE_URL; ?>user/wishlist.php">
                                    <i class="fas fa-heart me-2"></i>
                                    المفضلة
                                </a>
                            </li>
                            <?php if (is_admin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item professional-dropdown-item admin-link" href="<?php echo BASE_URL; ?>admin/">
                                        <i class="fas fa-cogs me-2"></i>
                                        لوحة الإدارة
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item professional-dropdown-item logout-link" href="<?php echo BASE_URL; ?>auth/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link professional-link login-link" href="<?php echo BASE_URL; ?>auth/login.php">
                            <span class="nav-icon">
                                <i class="fas fa-sign-in-alt"></i>
                            </span>
                            <span class="nav-text">دخول</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link professional-link register-link" href="<?php echo BASE_URL; ?>auth/register.php">
                            <span class="nav-icon">
                                <i class="fas fa-user-plus"></i>
                            </span>
                            <span class="nav-text">تسجيل</span>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

        </div>
    </div>
</nav>

<!-- Link to Professional Navbar CSS -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/professional-navbar.css">

<!-- Professional JavaScript for Navigation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.professional-navbar');
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.querySelector('.search-suggestions');
    const togglerButton = document.querySelector('.professional-toggler');

    // Navbar scroll effects
    let lastScrollTop = 0;
    let scrollTimeout;

    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Add scrolled class for styling
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Auto-hide navbar on scroll down, show on scroll up
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;

        // Clear loading animation after scroll
        clearTimeout(scrollTimeout);
        navbar.classList.add('navbar-loading');
        scrollTimeout = setTimeout(() => {
            navbar.classList.remove('navbar-loading');
        }, 150);
    }

    // Add transition to navbar
    navbar.style.transition = 'transform 0.3s ease, min-height 0.3s ease';

    // Throttled scroll event
    let ticking = false;
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(function() {
                handleScroll();
                ticking = false;
            });
            ticking = true;
        }
    });

    // Professional search functionality
    let searchTimeout;
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    fetchSearchSuggestions(query);
                }, 300);
            } else {
                hideSuggestions();
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.professional-search')) {
                hideSuggestions();
            }
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            const suggestions = searchSuggestions.querySelectorAll('.suggestion-item');
            let currentIndex = Array.from(suggestions).findIndex(item => item.classList.contains('active'));

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentIndex = currentIndex < suggestions.length - 1 ? currentIndex + 1 : 0;
                    updateActiveSuggestion(suggestions, currentIndex);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    currentIndex = currentIndex > 0 ? currentIndex - 1 : suggestions.length - 1;
                    updateActiveSuggestion(suggestions, currentIndex);
                    break;
                case 'Enter':
                    if (currentIndex >= 0 && suggestions[currentIndex]) {
                        e.preventDefault();
                        suggestions[currentIndex].click();
                    }
                    break;
                case 'Escape':
                    hideSuggestions();
                    searchInput.blur();
                    break;
            }
        });
    }

    function fetchSearchSuggestions(query) {
        fetch(`<?php echo BASE_URL; ?>api/search-suggestions.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.suggestions.length > 0) {
                showSuggestions(data.suggestions);
            } else {
                hideSuggestions();
            }
        })
        .catch(error => {
            console.error('Error fetching suggestions:', error);
            hideSuggestions();
        });
    }

    function showSuggestions(suggestions) {
        if (!searchSuggestions) return;

        searchSuggestions.innerHTML = suggestions.map(item => `
            <div class="suggestion-item" data-value="${item.name}" data-url="${item.url || ''}">
                <div class="suggestion-icon">
                    <i class="${item.icon || 'fas fa-search'}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-name">${item.name}</div>
                    <div class="suggestion-category">${item.category || 'منتج'}</div>
                </div>
            </div>
        `).join('');

        searchSuggestions.style.display = 'block';

        // Add click handlers
        searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const url = this.dataset.url;
                if (url) {
                    window.location.href = url;
                } else {
                    searchInput.value = this.dataset.value;
                    hideSuggestions();
                    searchInput.closest('form').submit();
                }
            });
        });
    }

    function hideSuggestions() {
        if (searchSuggestions) {
            searchSuggestions.style.display = 'none';
        }
    }

    function updateActiveSuggestion(suggestions, index) {
        suggestions.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
    }

    // Professional notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `navbar-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Expose notification function globally
    window.showNavbarNotification = showNotification;

    // Initialize tooltips for better UX
    const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element);
        });
    }

    // Enhanced Dropdown Behavior
    const dropdowns = document.querySelectorAll('.dropdown-toggle');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            // Close other dropdowns when opening a new one
            const otherDropdowns = document.querySelectorAll('.dropdown-toggle[aria-expanded="true"]');
            otherDropdowns.forEach(other => {
                if (other !== this) {
                    const otherDropdown = bootstrap.Dropdown.getInstance(other);
                    if (otherDropdown) {
                        otherDropdown.hide();
                    }
                }
            });
        });
    });

    // Mega menu hover behavior for desktop
    if (window.innerWidth > 991) {
        const megaDropdown = document.querySelector('.mega-dropdown');
        if (megaDropdown) {
            let hoverTimeout;

            megaDropdown.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                const dropdownToggle = this.querySelector('.dropdown-toggle');
                const dropdown = bootstrap.Dropdown.getOrCreateInstance(dropdownToggle);
                dropdown.show();
            });

            megaDropdown.addEventListener('mouseleave', function() {
                const dropdownToggle = this.querySelector('.dropdown-toggle');
                const dropdown = bootstrap.Dropdown.getInstance(dropdownToggle);
                if (dropdown) {
                    hoverTimeout = setTimeout(() => {
                        dropdown.hide();
                    }, 300);
                }
            });
        }
    }

    // User dropdown positioning
    const userDropdown = document.querySelector('.user-dropdown .dropdown-menu');
    if (userDropdown) {
        userDropdown.addEventListener('show.bs.dropdown', function() {
            // Ensure dropdown appears on the correct side in RTL
            this.classList.add('dropdown-menu-end');
        });
    }
});
</script>

</script>

<!-- Professional Navbar JavaScript -->
<script src="<?php echo BASE_URL; ?>assets/js/professional-navbar.js"></script>
<script src="<?php echo BASE_URL; ?>assets/js/navbar-search.js"></script>
