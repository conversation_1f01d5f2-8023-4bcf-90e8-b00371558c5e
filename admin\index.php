<?php
/**
 * Enhanced Admin Dashboard - لوحة تحكم الإدارة المحسنة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/Product.php';
require_once '../classes/Category.php';
require_once '../classes/Order.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$user_class = new User($db);
$product_class = new Product($db);
$category_class = new Category($db);
$order_class = new Order($db);

// Get comprehensive statistics
try {
    // User statistics
    $total_users = $user_class->getTotalCount();
    $new_users_today = $user_class->getNewUsersToday();
    $active_users = $user_class->getActiveUsersCount();

    // Product statistics
    $total_products = $product_class->getTotalCount();
    $active_products = $product_class->getActiveProductsCount();
    $low_stock_products = $product_class->getLowStockCount();

    // Category statistics
    $categories = $category_class->getAllCategories(false);
    $total_categories = count($categories);

    // Order statistics
    $total_orders = $order_class->getTotalOrdersCount(null, null);
    $pending_orders = $order_class->getPendingOrdersCount();
    $today_orders = $order_class->getTodayOrdersCount();
    $monthly_revenue = $order_class->getMonthlyRevenue();
    
    // Order stats array for easier access
    $order_stats = [
        'pending_orders' => $pending_orders,
        'processing_orders' => $order_class->getOrdersByStatus('processing'),
        'shipped_orders' => $order_class->getOrdersByStatus('shipped'),
        'delivered_orders' => $order_class->getOrdersByStatus('delivered')
    ];

    // Recent orders
    $recent_orders = $order_class->getAllOrders(1, 5);

    // Top selling products
    $top_products = $product_class->getTopSellingProducts(5);

    // Calculate growth percentages (mock data for now)
    $user_growth = 12.5;
    $order_growth = 8.3;
    $revenue_growth = 15.7;
    $product_growth = 5.2;

} catch (Exception $e) {
    // Set default values in case of error
    $total_users = 0;
    $new_users_today = 0;
    $active_users = 0;
    $total_products = 0;
    $active_products = 0;
    $low_stock_products = 0;
    $total_categories = 0;
    $total_orders = 0;
    $pending_orders = 0;
    $today_orders = 0;
    $monthly_revenue = 0;
    $recent_orders = [];
    $top_products = [];
    $order_stats = [
        'pending_orders' => 0,
        'processing_orders' => 0,
        'shipped_orders' => 0,
        'delivered_orders' => 0
    ];
    $user_growth = 0;
    $order_growth = 0;
    $revenue_growth = 0;
    $product_growth = 0;
}

$page_title = "لوحة تحكم الإدارة - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom Admin CSS -->
    <link href="assets/css/admin-dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    
    <!-- Admin Sidebar -->
    <nav class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <h4>لوحة التحكم</h4>
            <div class="subtitle">نظام إدارة المتجر</div>
        </div>
        
        <ul class="sidebar-nav nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="products/">
                    <i class="fas fa-box"></i>
                    المنتجات
                    <span class="badge bg-primary"><?php echo $total_products; ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="orders/">
                    <i class="fas fa-shopping-cart"></i>
                    الطلبات
                    <span class="badge bg-warning"><?php echo $pending_orders; ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="users/">
                    <i class="fas fa-users"></i>
                    المستخدمين
                    <span class="badge bg-info"><?php echo $total_users; ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="categories/">
                    <i class="fas fa-tags"></i>
                    التصنيفات
                    <span class="badge bg-secondary"><?php echo $total_categories; ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reports/">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="settings/">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link" href="../">
                    <i class="fas fa-globe"></i>
                    عرض الموقع
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <button class="sidebar-toggle" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h4 class="navbar-brand mb-0 ms-3">لوحة تحكم الإدارة</h4>
                    </div>
                    
                    <div class="d-flex align-items-center">
                        <span class="ms-3 text-muted">مرحباً، <?php echo $_SESSION['user_name'] ?? 'المدير'; ?></span>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="../"><i class="fas fa-globe ms-2"></i>عرض الموقع</a></li>
                                <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user ms-2"></i>الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="../logout.php"><i class="fas fa-sign-out-alt ms-2"></i>تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Dashboard Content -->
        <div class="container-fluid p-4">
            <!-- Welcome Card -->
            <div class="welcome-card dashboard-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="text-white mb-2">مرحباً بك في لوحة التحكم</h2>
                        <p class="text-white-50 mb-0">إدارة شاملة لمتجرك الإلكتروني مع إحصائيات مفصلة</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-chart-line fa-3x text-white-50"></i>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($total_users); ?></div>
                        <div class="stat-label">إجمالي المستخدمين</div>
                        <div class="stat-change <?php echo $user_growth > 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-arrow-<?php echo $user_growth > 0 ? 'up' : 'down'; ?>"></i>
                            <?php echo abs($user_growth); ?>% من الشهر الماضي
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($total_orders); ?></div>
                        <div class="stat-label">إجمالي الطلبات</div>
                        <div class="stat-change <?php echo $order_growth > 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-arrow-<?php echo $order_growth > 0 ? 'up' : 'down'; ?>"></i>
                            <?php echo abs($order_growth); ?>% من الشهر الماضي
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-value"><?php echo format_price($monthly_revenue); ?></div>
                        <div class="stat-label">إيرادات الشهر</div>
                        <div class="stat-change <?php echo $revenue_growth > 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-arrow-<?php echo $revenue_growth > 0 ? 'up' : 'down'; ?>"></i>
                            <?php echo abs($revenue_growth); ?>% من الشهر الماضي
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($active_products); ?></div>
                        <div class="stat-label">المنتجات النشطة</div>
                        <div class="stat-change <?php echo $product_growth > 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-arrow-<?php echo $product_growth > 0 ? 'up' : 'down'; ?>"></i>
                            <?php echo abs($product_growth); ?>% من الشهر الماضي
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">
                                <i class="fas fa-chart-line ms-2"></i>
                                نظرة عامة على الأداء
                            </h5>
                        </div>
                        <canvas id="performanceChart" height="100"></canvas>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">
                                <i class="fas fa-chart-pie ms-2"></i>
                                توزيع الطلبات
                            </h5>
                        </div>
                        <canvas id="ordersChart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="dashboard-card">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">
                                <i class="fas fa-clock ms-2"></i>
                                إحصائيات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <h6>طلبات اليوم</h6>
                                    <h3 class="text-primary"><?php echo $today_orders; ?></h3>
                                </div>
                                <div class="col-6 mb-3">
                                    <h6>مستخدمين جدد</h6>
                                    <h3 class="text-success"><?php echo $new_users_today; ?></h3>
                                </div>
                                <div class="col-6">
                                    <h6>مخزون منخفض</h6>
                                    <h3 class="text-warning"><?php echo $low_stock_products; ?></h3>
                                </div>
                                <div class="col-6">
                                    <h6>مستخدمين نشطين</h6>
                                    <h3 class="text-info"><?php echo $active_users; ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="dashboard-card">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">
                                <i class="fas fa-list ms-2"></i>
                                حالة الطلبات
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <h6>قيد الانتظار</h6>
                                    <h3 class="text-warning"><?php echo $order_stats['pending_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>قيد المعالجة</h6>
                                    <h3 class="text-info"><?php echo $order_stats['processing_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>تم الشحن</h6>
                                    <h3 class="text-primary"><?php echo $order_stats['shipped_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>تم التسليم</h6>
                                    <h3 class="text-success"><?php echo $order_stats['delivered_orders']; ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Recent Orders and Top Products -->
            <div class="row">
                <div class="col-lg-7">
                    <div class="data-table">
                        <div class="table-header">
                            <h5 class="table-title">
                                <i class="fas fa-shopping-cart ms-2"></i>
                                أحدث الطلبات
                            </h5>
                        </div>
                        <?php if (!empty($recent_orders)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>العميل</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><strong><?php echo $order['order_number']; ?></strong></td>
                                                <td><?php echo $order['customer_name']; ?></td>
                                                <td><?php echo format_price($order['total_amount']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = 'status-' . $order['status'];
                                                    $status_text = '';
                                                    switch ($order['status']) {
                                                        case 'pending': $status_text = 'قيد الانتظار'; break;
                                                        case 'processing': $status_text = 'قيد المعالجة'; break;
                                                        case 'shipped': $status_text = 'تم الشحن'; break;
                                                        case 'delivered': $status_text = 'تم التسليم'; break;
                                                        case 'cancelled': $status_text = 'ملغي'; break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-secondary"><?php echo $status_text; ?></span>
                                                </td>
                                                <td><?php echo date('Y/m/d', strtotime($order['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h6>لا توجد طلبات حديثة</h6>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-lg-5">
                    <div class="data-table">
                        <div class="table-header">
                            <h5 class="table-title">
                                <i class="fas fa-star ms-2"></i>
                                المنتجات الأكثر مبيعاً
                            </h5>
                        </div>
                        <?php if (!empty($top_products)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>المبيعات</th>
                                            <th>السعر</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_products as $product): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo $product['primary_image'] ? ASSETS_URL . '/images/products/' . $product['primary_image'] : ASSETS_URL . '/images/placeholder.svg'; ?>"
                                                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars(substr($product['name'], 0, 25)) . (strlen($product['name']) > 25 ? '...' : ''); ?></h6>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-primary"><?php echo number_format($product['total_sold']); ?></span></td>
                                                <td><?php echo format_price($product['price']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h6>لا توجد بيانات مبيعات</h6>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('adminSidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
            overlay.classList.toggle('show');
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay').addEventListener('click', function() {
            const sidebar = document.getElementById('adminSidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('show');
            mainContent.classList.remove('sidebar-open');
            overlay.classList.remove('show');
        });

        // Performance Chart
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المبيعات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'الطلبات',
                    data: [65, 89, 72, 105, 98, 125],
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Orders Chart
        const ordersCtx = document.getElementById('ordersChart').getContext('2d');
        const ordersChart = new Chart(ordersCtx, {
            type: 'doughnut',
            data: {
                labels: ['قيد الانتظار', 'قيد المعالجة', 'تم الشحن', 'تم التسليم'],
                datasets: [{
                    data: [
                        <?php echo $order_stats['pending_orders']; ?>,
                        <?php echo $order_stats['processing_orders']; ?>,
                        <?php echo $order_stats['shipped_orders']; ?>,
                        <?php echo $order_stats['delivered_orders']; ?>
                    ],
                    backgroundColor: [
                        '#f39c12',
                        '#3498db',
                        '#9b59b6',
                        '#27ae60'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Auto refresh every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
            </div>
