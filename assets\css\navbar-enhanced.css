/* Enhanced Navbar Dropdown Styles - أنماط القوائم المنسدلة المحسنة */

/* RTL Dropdown Improvements */
.dropdown-menu {
    border: none;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    border-radius: 15px;
    padding: 1rem 0;
    margin-top: 0.5rem;
    min-width: 250px;
    animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Categories Mega Menu Enhanced */
.mega-dropdown .dropdown-menu {
    position: static;
    float: none;
    width: 100%;
    margin-top: 0;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.mega-menu {
    background: white;
    padding: 2rem;
    min-height: 300px;
}

.mega-menu-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.mega-menu-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.3rem;
    margin: 0;
}

.mega-menu-item {
    display: block;
    padding: 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    color: #2c3e50;
    background: #f8f9fa;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mega-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: right 0.4s ease;
    z-index: 1;
}

.mega-menu-item:hover::before {
    right: 0;
}

.mega-menu-item:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.mega-menu-item > * {
    position: relative;
    z-index: 2;
}

.mega-item-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.mega-menu-item:hover .mega-item-icon {
    background: white;
    color: #667eea;
    transform: scale(1.1);
}

.mega-item-content h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.mega-item-content p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    transition: color 0.3s ease;
}

.mega-menu-item:hover .mega-item-content p {
    color: rgba(255, 255, 255, 0.8);
}

.mega-item-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 3;
}

.mega-menu-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 2px solid #f8f9fa;
    text-align: center;
}

/* User Dropdown Enhanced */
.user-dropdown .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 280px;
    padding: 0;
    overflow: hidden;
}

.user-dropdown-menu {
    background: white;
    border-radius: 15px;
}

.dropdown-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border: none;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-details small {
    color: rgba(255, 255, 255, 0.8);
}

.professional-dropdown-item {
    padding: 1rem 1.5rem;
    color: #2c3e50;
    transition: all 0.3s ease;
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.professional-dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #667eea;
    padding-right: 2rem;
}

.professional-dropdown-item i {
    width: 20px;
    text-align: center;
    color: #667eea;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: #f8f9fa;
}

/* Regular Dropdown Enhanced */
.professional-dropdown {
    right: 0;
    left: auto;
    min-width: 220px;
    padding: 0.5rem 0;
}

.professional-dropdown .professional-dropdown-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .dropdown-menu {
        backdrop-filter: none;
        background: white;
        border: 1px solid #dee2e6;
    }

    .mega-dropdown .dropdown-menu {
        position: relative;
        width: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        margin-top: 0.5rem;
        background: white;
    }

    .mega-menu {
        padding: 1rem;
        min-height: auto;
    }

    .mega-menu-item {
        margin-bottom: 0.5rem;
        padding: 1rem;
        font-size: 0.95rem;
    }

    .mega-item-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .user-dropdown .dropdown-menu {
        right: 0;
        left: auto;
        width: 280px;
        max-width: 90vw;
        margin-top: 0.5rem;
        border-radius: 15px;
    }

    .professional-dropdown {
        right: 0;
        left: auto;
        width: 250px;
        max-width: 85vw;
        margin-top: 0.5rem;
        border-radius: 15px;
    }

    /* Mobile touch improvements */
    .dropdown-item {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .professional-dropdown-item {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
}

/* RTL Specific Adjustments */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

[dir="rtl"] .dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

[dir="rtl"] .mega-dropdown .dropdown-menu {
    right: auto;
    left: 0;
    text-align: right;
}

[dir="rtl"] .user-dropdown .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

[dir="rtl"] .professional-dropdown {
    right: 0 !important;
    left: auto !important;
}

[dir="rtl"] .professional-dropdown-item:hover {
    padding-right: 1.5rem;
    padding-left: 2rem;
}

[dir="rtl"] .mega-menu-item::before {
    right: -100%;
    left: auto;
}

[dir="rtl"] .mega-menu-item:hover::before {
    right: 0;
    left: auto;
}

[dir="rtl"] .mega-item-badge {
    right: 15px;
    left: auto;
}

/* Enhanced RTL Support for Bootstrap Dropdowns */
.dropdown-menu[data-bs-popper] {
    right: 0;
    left: auto;
}

.dropdown-menu-end[data-bs-popper] {
    right: 0 !important;
    left: auto !important;
}

/* Dropdown Arrow Indicators */
.dropdown-toggle::after {
    border: none;
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.dropdown-toggle[aria-expanded="true"]::after {
    transform: rotate(180deg);
}

/* Hover Effects for Nav Items */
.nav-item:hover .dropdown-menu {
    display: block;
    animation: dropdownFadeIn 0.3s ease-out;
}

.mega-dropdown:hover .dropdown-menu {
    display: block;
}

.user-dropdown:hover .dropdown-menu {
    display: block;
}

/* Loading States */
.dropdown-menu.loading {
    opacity: 0.7;
    pointer-events: none;
}

.dropdown-menu.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.dropdown-item:focus {
    background-color: #667eea;
    color: white;
    outline: 2px solid #764ba2;
    outline-offset: -2px;
}

.mega-menu-item:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    .dropdown-menu {
        background: #2c3e50;
        color: white;
    }
    
    .professional-dropdown-item {
        color: white;
    }
    
    .professional-dropdown-item:hover {
        background: #34495e;
        color: #667eea;
    }
}
