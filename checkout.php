<?php
/**
 * Checkout Page - صفحة إتمام الطلب
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';
require_once 'classes/Order.php';
require_once 'classes/User.php';

// Check if cart is not empty
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    header('Location: cart.php');
    exit();
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$order_class = new Order($db);
$user_class = new User($db);

// Get user info if logged in
$user_info = null;
if (is_logged_in()) {
    $user_info = $user_class->getUserById($_SESSION['user_id']);
}

// Calculate cart totals
$cart_items = array();
$cart_total = 0;
$shipping_cost = 25.00;
$tax_rate = 0.15;

foreach ($_SESSION['cart'] as $cart_key => $quantity) {
    $parts = explode('_', $cart_key);
    $product_id = (int)$parts[0];
    $variant_id = isset($parts[1]) ? (int)$parts[1] : null;
    
    $product = $product_class->getProductById($product_id);
    if ($product) {
        $item_price = $product['sale_price'] ?: $product['price'];
        $item_total = $item_price * $quantity;
        
        $cart_items[] = array(
            'product_id' => $product_id,
            'variant_id' => $variant_id,
            'name' => $product['name'],
            'sku' => $product['sku'],
            'price' => $item_price,
            'quantity' => $quantity,
            'total' => $item_total
        );
        
        $cart_total += $item_total;
    }
}

$subtotal = $cart_total;
$tax_amount = $cart_total * $tax_rate;
$final_total = $cart_total + $shipping_cost + $tax_amount;

// Handle form submission
$errors = array();
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['place_order'])) {
    // Validate form data
    $customer_name = sanitize_input($_POST['customer_name'] ?? '');
    $customer_email = sanitize_input($_POST['customer_email'] ?? '');
    $customer_phone = sanitize_input($_POST['customer_phone'] ?? '');
    $shipping_address = sanitize_input($_POST['shipping_address'] ?? '');
    $city = sanitize_input($_POST['city'] ?? '');
    $notes = sanitize_input($_POST['notes'] ?? '');
    
    // Validation
    if (empty($customer_name)) $errors[] = "الاسم مطلوب";
    if (empty($customer_email) || !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "البريد الإلكتروني غير صحيح";
    }
    if (empty($customer_phone)) $errors[] = "رقم الهاتف مطلوب";
    if (empty($shipping_address)) $errors[] = "عنوان الشحن مطلوب";
    if (empty($city)) $errors[] = "المدينة مطلوبة";
    
    if (empty($errors)) {
        // Prepare order data
        $order_data = array(
            'user_id' => is_logged_in() ? $_SESSION['user_id'] : null,
            'total_amount' => $final_total,
            'shipping_cost' => $shipping_cost,
            'tax_amount' => $tax_amount,
            'shipping_address' => $shipping_address . ', ' . $city,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'customer_phone' => $customer_phone,
            'notes' => $notes
        );
        
        // Prepare order items
        $order_items = array();
        foreach ($cart_items as $item) {
            $order_items[] = array(
                'product_id' => $item['product_id'],
                'variant_id' => $item['variant_id'],
                'product_name' => $item['name'],
                'product_sku' => $item['sku'],
                'quantity' => $item['quantity'],
                'unit_price' => $item['price'],
                'total_price' => $item['total'],
                'product_attributes' => null // Can be enhanced for variants
            );
        }
        
        // Create order
        $order_id = $order_class->createOrder($order_data, $order_items);
        
        if ($order_id) {
            // Clear cart
            clear_cart();
            $success = true;
            $order_number = $order_class->getOrderById($order_id)['order_number'];
        } else {
            $errors[] = "حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.";
        }
    }
}

$page_title = "إتمام الطلب - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .checkout-steps {
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: #0d6efd;
        }
        
        .step.completed .step-number {
            background: #198754;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            position: sticky;
            top: 100px;
        }
        
        .form-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .success-animation {
            text-align: center;
            padding: 3rem 0;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #198754;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-shopping-bag me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="cart.php">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للسلة
                </a>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="cart.php">عربة التسوق</a></li>
                <li class="breadcrumb-item active">إتمام الطلب</li>
            </ol>
        </nav>
    </div>

    <div class="container py-4">
        <?php if ($success): ?>
            <!-- Success Message -->
            <div class="success-animation">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="text-success mb-3">تم إنشاء طلبك بنجاح!</h2>
                <p class="lead mb-4">رقم الطلب: <strong><?php echo $order_number; ?></strong></p>
                <p class="text-muted mb-4">
                    سيتم التواصل معك قريباً لتأكيد الطلب وترتيب عملية التوصيل.
                    <br>
                    تم إرسال تفاصيل الطلب إلى بريدك الإلكتروني.
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <?php if (is_logged_in()): ?>
                        <a href="user/orders.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>
                            متابعة الطلبات
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <!-- Checkout Steps -->
            <div class="checkout-steps">
                <div class="row">
                    <div class="col-md-4">
                        <div class="step completed">
                            <div class="step-number">1</div>
                            <span>عربة التسوق</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="step active">
                            <div class="step-number">2</div>
                            <span>معلومات الشحن</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="step">
                            <div class="step-number">3</div>
                            <span>تأكيد الطلب</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية:</h6>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Checkout Form -->
                <div class="col-lg-8">
                    <form method="POST" id="checkoutForm">
                        <!-- Customer Information -->
                        <div class="form-section">
                            <h4 class="mb-4">
                                <i class="fas fa-user me-2"></i>
                                معلومات العميل
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customer_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                           value="<?php echo $user_info ? $user_info['first_name'] . ' ' . $user_info['last_name'] : ($_POST['customer_name'] ?? ''); ?>" 
                                           required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="customer_email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="customer_email" name="customer_email" 
                                           value="<?php echo $user_info ? $user_info['email'] : ($_POST['customer_email'] ?? ''); ?>" 
                                           required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="customer_phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="customer_phone" name="customer_phone" 
                                           value="<?php echo $user_info ? $user_info['phone'] : ($_POST['customer_phone'] ?? ''); ?>" 
                                           required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="city" class="form-label">المدينة *</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="<?php echo $user_info ? $user_info['city'] : ($_POST['city'] ?? ''); ?>" 
                                           required>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="form-section">
                            <h4 class="mb-4">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                عنوان الشحن
                            </h4>
                            
                            <div class="mb-3">
                                <label for="shipping_address" class="form-label">العنوان التفصيلي *</label>
                                <textarea class="form-control" id="shipping_address" name="shipping_address" 
                                          rows="3" required placeholder="مثال: شارع الملك فهد، حي النزهة، مبنى رقم 123، الدور الثاني"><?php echo $user_info ? $user_info['address'] : ($_POST['shipping_address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات إضافية (اختياري)</label>
                                <textarea class="form-control" id="notes" name="notes" 
                                          rows="2" placeholder="أي ملاحظات خاصة بالطلب أو التوصيل"><?php echo $_POST['notes'] ?? ''; ?></textarea>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="form-section">
                            <h4 class="mb-4">
                                <i class="fas fa-credit-card me-2"></i>
                                طريقة الدفع
                            </h4>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="cod" value="cod" checked>
                                <label class="form-check-label" for="cod">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    الدفع عند الاستلام
                                    <small class="text-muted d-block">ادفع نقداً عند وصول الطلب</small>
                                </label>
                            </div>
                        </div>

                        <!-- Place Order Button -->
                        <div class="d-grid">
                            <button type="submit" name="place_order" class="btn btn-success btn-lg">
                                <i class="fas fa-check me-2"></i>
                                تأكيد الطلب - <?php echo format_price($final_total); ?>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="order-summary">
                        <h5 class="mb-4">ملخص الطلب</h5>
                        
                        <!-- Order Items -->
                        <div class="mb-3">
                            <?php foreach ($cart_items as $item): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <h6 class="mb-0"><?php echo $item['name']; ?></h6>
                                        <small class="text-muted">الكمية: <?php echo $item['quantity']; ?></small>
                                    </div>
                                    <span><?php echo format_price($item['total']); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <hr>
                        
                        <!-- Totals -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span><?php echo format_price($subtotal); ?></span>
                        </div>
                        
                        <?php $current_currency = get_current_currency(); ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span><?php echo format_price($shipping_cost, $current_currency); ?></span>
                        </div>

                        <div class="d-flex justify-content-between mb-3">
                            <span>الضريبة (15%):</span>
                            <span><?php echo format_price($tax_amount, $current_currency); ?></span>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between mb-4">
                            <strong>الإجمالي النهائي:</strong>
                            <strong class="text-success h5"><?php echo format_price($final_total, $current_currency); ?></strong>
                        </div>
                        
                        <!-- Security Info -->
                        <div class="p-3 bg-light rounded">
                            <h6 class="mb-2">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                طلبك آمن
                            </h6>
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-1"></i> معلوماتك محمية</li>
                                <li><i class="fas fa-check text-success me-1"></i> دفع آمن</li>
                                <li><i class="fas fa-check text-success me-1"></i> ضمان الاسترداد</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('checkoutForm').addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
            }
        });
        
        // Phone number formatting
        document.getElementById('customer_phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.startsWith('966')) {
                    value = '+' + value;
                } else if (value.startsWith('05')) {
                    value = '+966' + value.substring(1);
                } else if (value.startsWith('5')) {
                    value = '+966' + value;
                }
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
