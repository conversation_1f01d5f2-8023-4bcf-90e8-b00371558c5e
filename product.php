<?php
/**
 * Product Details Page - صفحة تفاصيل المنتج
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/image_helper.php';
require_once 'classes/Product.php';
require_once 'classes/Category.php';
require_once 'classes/ProductVariant.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$category = new Category($db);
$variant_class = new ProductVariant($db);

// Get product ID from URL (supports both ID and slug)
$product_id = 0;

if (isset($_GET['id'])) {
    $product_id = (int)$_GET['id'];
} elseif (isset($_GET['slug'])) {
    $product_id = extract_product_id_from_slug($_GET['slug']);
}

// Check if we're using .htaccess rewrite (product/slug-id format)
$request_uri = $_SERVER['REQUEST_URI'];
if (preg_match('/\/product\/([^\/\?]+)/', $request_uri, $matches)) {
    $slug_or_id = $matches[1];
    $product_id = extract_product_id_from_slug($slug_or_id);
}

if (!$product_id) {
    header('Location: ' . BASE_URL);
    exit();
}

// Get product details
$product = $product_class->getProductById($product_id);

if (!$product) {
    header('Location: ' . BASE_URL);
    exit();
}

// Get product variants and attributes
$product_variants = $variant_class->getProductVariants($product_id);
$available_attributes = $variant_class->getAvailableAttributes($product_id);

// Get product images
$product_images = $product_class->getProductImages($product_id);

// Get product variants (temporarily disabled until tables are created)
$product_variants = [];

// Get related products
$related_products = $product_class->getRelatedProducts($product_id, $product['category_id'], 4);

// Handle add to cart
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
    $variant_id = isset($_POST['variant_id']) ? (int)$_POST['variant_id'] : null;
    
    if ($quantity > 0) {
        add_to_cart($product_id, $variant_id, $quantity);
        $success_message = "تم إضافة المنتج إلى السلة بنجاح!";
    }
}

$page_title = $product['name'] . " - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($product['meta_description'] ?: $product['short_description']); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Swiper CSS for Image Gallery -->
    <link href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    <!-- Enhanced Product CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/product-enhanced.css" rel="stylesheet">
    
    <style>
        .product-image-main {
            height: 500px;
            object-fit: cover;
            cursor: zoom-in;
        }
        
        .product-thumbnail {
            height: 80px;
            object-fit: cover;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.3s;
        }
        
        .product-thumbnail.active,
        .product-thumbnail:hover {
            border-color: #667eea;
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        /* Enhanced Gallery Styles */
        .product-gallery {
            position: relative;
        }

        .main-image-container {
            position: relative;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .product-image-main:hover {
            transform: scale(1.02);
        }

        .sale-badge {
            z-index: 10;
        }

        .zoom-icon {
            z-index: 10;
        }

        /* Swiper Customization */
        .main-swiper {
            border-radius: 1rem;
            overflow: hidden;
        }

        .thumbnail-swiper {
            margin-top: 1rem;
        }

        .thumbnail-swiper .swiper-slide {
            width: auto;
            margin-right: 0.5rem;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: #667eea;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-top: -20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 16px;
            font-weight: bold;
        }

        .swiper-pagination-bullet {
            background: #667eea;
            opacity: 0.5;
        }

        .swiper-pagination-bullet-active {
            opacity: 1;
            background: #667eea;
        }
        
        .product-info {
            position: sticky;
            top: 100px;
        }
        
        .price-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .variant-option {
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .variant-option:hover {
            background-color: #e9ecef;
        }
        
        .variant-option.selected {
            background-color: #0d6efd;
            color: white;
        }
        
        .related-product-card {
            transition: transform 0.3s ease;
        }
        
        .related-product-card:hover {
            transform: translateY(-5px);
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
        }
        
        .zoom-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .zoom-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="?category=<?php echo $product['category_id']; ?>"><?php echo $product['category_name']; ?></a></li>
                <li class="breadcrumb-item active"><?php echo $product['name']; ?></li>
            </ol>
        </nav>
    </div>

    <!-- Success Message -->
    <?php if (isset($success_message)): ?>
        <div class="container">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Product Details -->
    <div class="container py-4">
        <div class="row">
            <!-- Enhanced Product Images Gallery -->
            <div class="col-lg-6 mb-4">
                <div class="product-gallery">
                    <?php if (!empty($product_images)): ?>
                        <!-- Main Image with Zoom -->
                        <div class="main-image-container position-relative mb-3">
                            <div class="swiper main-swiper">
                                <div class="swiper-wrapper">
                                    <?php foreach ($product_images as $index => $image): ?>
                                        <div class="swiper-slide">
                                            <?php echo generate_product_image_html(
                                                $image['image_path'],
                                                $product['name'],
                                                'img-fluid rounded product-image-main w-100',
                                                'height: 400px; object-fit: cover; cursor: zoom-in;',
                                                false
                                            ); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Navigation buttons -->
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>

                                <!-- Pagination -->
                                <div class="swiper-pagination"></div>
                            </div>

                            <!-- Zoom Icon -->
                            <div class="zoom-icon position-absolute top-0 end-0 m-3">
                                <button class="btn btn-light btn-sm rounded-circle" onclick="openImageModal(0)">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>

                            <!-- Sale Badge -->
                            <?php if ($product['sale_price']): ?>
                                <div class="sale-badge position-absolute top-0 start-0 m-3">
                                    <span class="badge bg-danger fs-6">
                                        خصم <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>%
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Thumbnail Gallery -->
                        <?php if (count($product_images) > 1): ?>
                            <div class="swiper thumbnail-swiper">
                                <div class="swiper-wrapper">
                                    <?php foreach ($product_images as $index => $image): ?>
                                        <div class="swiper-slide">
                                            <?php echo generate_product_image_html(
                                                $image['image_path'],
                                                $product['name'],
                                                'img-fluid rounded product-thumbnail',
                                                'height: 80px; object-fit: cover; cursor: pointer;',
                                                false
                                            ); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="main-image-container">
                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                 style="height: 400px;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-image fa-5x mb-3"></i>
                                    <p>لا توجد صور للمنتج</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Product Info -->
            <div class="col-lg-6">
                <div class="product-info">
                    <h1 class="h2 mb-3"><?php echo $product['name']; ?></h1>
                    
                    <div class="mb-3">
                        <span class="badge bg-secondary"><?php echo $product['category_name']; ?></span>
                        <?php if ($product['sku']): ?>
                            <span class="text-muted ms-3">رقم المنتج: <?php echo $product['sku']; ?></span>
                        <?php endif; ?>
                    </div>

                    <!-- Price -->
                    <div class="price-section">
                        <?php
                        $current_currency = get_current_currency();
                        if ($product['sale_price']): ?>
                            <div class="d-flex align-items-center gap-3">
                                <span class="h3 text-danger mb-0"><?php echo format_price($product['sale_price'], $current_currency); ?></span>
                                <span class="h5 text-muted text-decoration-line-through mb-0"><?php echo format_price($product['price'], $current_currency); ?></span>
                                <span class="badge bg-danger">
                                    خصم <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>%
                                </span>
                            </div>
                        <?php else: ?>
                            <span class="h3 text-primary mb-0"><?php echo format_price($product['price'], $current_currency); ?></span>
                        <?php endif; ?>
                    </div>

                    <!-- Stock Status -->
                    <div class="mb-3">
                        <?php if ($product['stock_quantity'] > 0): ?>
                            <span class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                متوفر في المخزون (<?php echo $product['stock_quantity']; ?> قطعة)
                            </span>
                        <?php else: ?>
                            <span class="text-danger">
                                <i class="fas fa-times-circle me-1"></i>
                                غير متوفر حالياً
                            </span>
                        <?php endif; ?>
                    </div>

                    <!-- Product Variants -->
                    <?php if (!empty($available_attributes)): ?>
                        <div class="product-variants">
                            <?php foreach ($available_attributes as $attribute): ?>
                                <div class="variant-group">
                                    <label class="variant-label"><?php echo $attribute['display_name']; ?>:</label>
                                    <div class="variant-options">
                                        <?php foreach ($attribute['values'] as $value): ?>
                                            <div class="variant-option <?php echo $attribute['name'] === 'color' ? 'color-variant' : ''; ?>"
                                                 data-attribute-id="<?php echo $attribute['id']; ?>"
                                                 data-value-id="<?php echo $value['id']; ?>"
                                                 data-value="<?php echo $value['value']; ?>"
                                                 <?php if ($attribute['name'] === 'color' && $value['color_code']): ?>
                                                     style="background-color: <?php echo $value['color_code']; ?>;"
                                                 <?php endif; ?>>
                                                <?php if ($attribute['name'] !== 'color'): ?>
                                                    <?php echo $value['display_value']; ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Quantity Selector -->
                    <div class="quantity-selector">
                        <label class="quantity-label">الكمية:</label>
                        <div class="quantity-controls">
                            <button type="button" class="quantity-btn" id="decreaseQty">-</button>
                            <input type="number" class="quantity-input" id="quantity" name="quantity"
                                   value="1" min="1" max="<?php echo $product['stock_quantity']; ?>" readonly>
                            <button type="button" class="quantity-btn" id="increaseQty">+</button>
                        </div>
                    </div>

                    <!-- Product Actions -->
                    <div class="product-actions">
                        <?php if ($product['stock_quantity'] > 0): ?>
                            <form method="POST" id="addToCartForm">
                                <input type="hidden" name="variant_id" id="selectedVariant" value="">
                                <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">
                                <input type="hidden" name="selected_quantity" id="selectedQuantity" value="1">

                                <button type="submit" name="add_to_cart" class="btn btn-add-to-cart w-100 mb-3">
                                    <i class="fas fa-cart-plus me-2"></i>
                                    أضف إلى السلة
                                </button>
                            </form>

                            <button type="button" class="btn btn-buy-now w-100 mb-3" onclick="buyNow(<?php echo $product_id; ?>)">
                                <i class="fas fa-bolt me-2"></i>
                            </button>

                            <button type="button" class="btn btn-wishlist w-100" id="wishlistBtn">
                                <i class="fas fa-heart me-2"></i>
                                أضف للمفضلة
                            </button>
                        <?php else: ?>
                            <div class="alert alert-warning text-center">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                المنتج غير متوفر حالياً
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Product Features -->
                    <div class="product-features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div class="feature-text">شحن مجاني للطلبات أكثر من 200 ريال</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div class="feature-text">إمكانية الإرجاع خلال 30 يوم</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="feature-text">ضمان الجودة والأصالة</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="feature-text">دعم فني على مدار الساعة</div>
                        </div>
                    </div>

                    <!-- Product Description -->
                    <div class="mb-4">
                        <h5>وصف المنتج</h5>
                        <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                    </div>

                    <!-- Product Specifications -->
                    <?php if ($product['weight'] || $product['dimensions']): ?>
                        <div class="mb-4">
                            <h6>المواصفات</h6>
                            <ul class="list-unstyled">
                                <?php if ($product['weight']): ?>
                                    <li><strong>الوزن:</strong> <?php echo $product['weight']; ?> كجم</li>
                                <?php endif; ?>
                                <?php if ($product['dimensions']): ?>
                                    <li><strong>الأبعاد:</strong> <?php echo $product['dimensions']; ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    <?php if (!empty($related_products)): ?>
        <section class="py-5 bg-light">
            <div class="container">
                <h3 class="text-center mb-5">منتجات ذات صلة</h3>
                <div class="row">
                    <?php foreach ($related_products as $related): ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card related-product-card h-100 border-0 shadow-sm">
                                <?php if ($related['primary_image']): ?>
                                    <img src="<?php echo ASSETS_URL; ?>images/products/<?php echo $related['primary_image']; ?>"
                                         class="card-img-top" style="height: 200px; object-fit: cover;"
                                         alt="<?php echo $related['name']; ?>">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title"><?php echo $related['name']; ?></h6>
                                    <p class="card-text text-muted small flex-grow-1">
                                        <?php echo substr($related['short_description'], 0, 80); ?>...
                                    </p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="h6 text-primary mb-0">
                                                <?php echo format_price($related['sale_price'] ?: $related['price'], $current_currency); ?>
                                            </span>
                                        </div>
                                        <a href="product.php?id=<?php echo $related['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm w-100">عرض المنتج</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Image Zoom Overlay -->
    <div class="zoom-overlay" id="zoomOverlay" onclick="closeZoom()">
        <img src="" alt="" class="zoom-image" id="zoomImage">
        <button class="btn btn-light position-absolute top-0 end-0 m-3" onclick="closeZoom()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Image Zoom Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-transparent border-0">
                <div class="modal-header border-0">
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center p-0">
                    <div class="swiper modal-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($product_images as $index => $image): ?>
                                <div class="swiper-slide">
                                    <?php echo generate_product_image_html(
                                        $image['image_path'],
                                        $product['name'],
                                        'img-fluid rounded',
                                        'max-height: 80vh; width: auto;',
                                        false
                                    ); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>js/script.js"></script>

    <script>
        // Initialize Swiper for main gallery
        let mainSwiper = null;
        let thumbnailSwiper = null;
        let modalSwiper = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Main image swiper
            mainSwiper = new Swiper('.main-swiper', {
                spaceBetween: 10,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                thumbs: {
                    swiper: thumbnailSwiper
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                loop: <?php echo count($product_images) > 1 ? 'true' : 'false'; ?>,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
            });

            // Thumbnail swiper
            if (document.querySelector('.thumbnail-swiper')) {
                thumbnailSwiper = new Swiper('.thumbnail-swiper', {
                    spaceBetween: 10,
                    slidesPerView: 'auto',
                    freeMode: true,
                    watchSlidesProgress: true,
                });

                // Connect main and thumbnail swipers
                mainSwiper.thumbs.swiper = thumbnailSwiper;
            }

            // Modal swiper
            modalSwiper = new Swiper('.modal-swiper', {
                spaceBetween: 10,
                navigation: {
                    nextEl: '.modal-swiper .swiper-button-next',
                    prevEl: '.modal-swiper .swiper-button-prev',
                },
                pagination: {
                    el: '.modal-swiper .swiper-pagination',
                    clickable: true,
                },
                loop: <?php echo count($product_images) > 1 ? 'true' : 'false'; ?>,
                keyboard: {
                    enabled: true,
                },
            });
        });

        // Open image modal
        function openImageModal(index = 0) {
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();

            // Sync modal swiper with main swiper
            if (modalSwiper && mainSwiper) {
                modalSwiper.slideTo(mainSwiper.activeIndex);
            }
        }

        // Click on main image to open modal
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('product-image-main')) {
                openImageModal();
            }
        });

        // Keyboard navigation for modal
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (modal.classList.contains('show')) {
                if (e.key === 'ArrowLeft' && modalSwiper) {
                    modalSwiper.slideNext();
                } else if (e.key === 'ArrowRight' && modalSwiper) {
                    modalSwiper.slidePrev();
                } else if (e.key === 'Escape') {
                    bootstrap.Modal.getInstance(modal).hide();
                }
            }
        });

        // Variant selection
        document.querySelectorAll('.variant-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all options
                document.querySelectorAll('.variant-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Add active class to selected option
                this.classList.add('selected');
                
                // Update hidden input
                document.getElementById('selectedVariant').value = this.dataset.variantId;
                
                // Update quantity max value based on variant stock
                const quantityInput = document.getElementById('quantity');
                quantityInput.max = this.dataset.stock;
                
                if (parseInt(quantityInput.value) > parseInt(this.dataset.stock)) {
                    quantityInput.value = this.dataset.stock;
                }
            });
        });

        // Prevent zoom overlay from closing when clicking on image
        document.getElementById('zoomImage').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Buy Now function
        function buyNow(productId) {
            const quantityInput = document.getElementById('quantity');
            const variantInput = document.getElementById('selectedVariant');
            const quantity = quantityInput ? quantityInput.value : 1;
            const variantId = variantInput ? variantInput.value : '';

            // Add to cart first
            const formData = new FormData();
            formData.append('action', 'add');
            formData.append('product_id', productId);
            formData.append('quantity', quantity);

            if (variantId) {
                formData.append('variant_id', variantId);
            }

            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
            button.disabled = true;

            fetch('api/cart.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to checkout
                    window.location.href = 'checkout.php';
                } else {
                    alert('حدث خطأ: ' + (data.message || 'لم يتم إضافة المنتج'));
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Product Variants JavaScript
        const variantOptions = document.querySelectorAll('.variant-option');
        const quantityInput = document.getElementById('quantity');
        const selectedQuantityInput = document.getElementById('selectedQuantity');
        const selectedVariantInput = document.getElementById('selectedVariant');
        const decreaseBtn = document.getElementById('decreaseQty');
        const increaseBtn = document.getElementById('increaseQty');
        const wishlistBtn = document.getElementById('wishlistBtn');

        let selectedVariants = {};
        let currentPrice = <?php echo $product['sale_price'] ?: $product['price']; ?>;
        let maxStock = <?php echo $product['stock_quantity']; ?>;

        // Handle variant selection
        variantOptions.forEach(option => {
            option.addEventListener('click', function() {
                const attributeId = this.dataset.attributeId;
                const valueId = this.dataset.valueId;
                const value = this.dataset.value;

                // Remove selection from same attribute group
                document.querySelectorAll(`[data-attribute-id="${attributeId}"]`).forEach(opt => {
                    opt.classList.remove('selected');
                });

                // Add selection to clicked option
                this.classList.add('selected');
                selectedVariants[attributeId] = {
                    valueId: valueId,
                    value: value
                };

                updateVariantInfo();
            });
        });

        // Update variant information
        function updateVariantInfo() {
            // Find matching variant
            const selectedAttributeIds = Object.keys(selectedVariants).map(key => selectedVariants[key].valueId);

            // Here you would typically make an AJAX call to find the exact variant
            // For now, we'll update the hidden input with selected attributes
            selectedVariantInput.value = selectedAttributeIds.join(',');

            // Update price display if needed
            updatePriceDisplay();
        }

        // Update price display
        function updatePriceDisplay() {
            const priceElement = document.querySelector('.current-price');
            if (priceElement) {
                // You can implement dynamic pricing based on selected variants
                priceElement.textContent = formatPrice(currentPrice);
            }
        }

        // Quantity controls
        decreaseBtn.addEventListener('click', function() {
            let currentQty = parseInt(quantityInput.value);
            if (currentQty > 1) {
                quantityInput.value = currentQty - 1;
                selectedQuantityInput.value = currentQty - 1;
            }
        });

        increaseBtn.addEventListener('click', function() {
            let currentQty = parseInt(quantityInput.value);
            if (currentQty < maxStock) {
                quantityInput.value = currentQty + 1;
                selectedQuantityInput.value = currentQty + 1;
            }
        });

        quantityInput.addEventListener('change', function() {
            selectedQuantityInput.value = this.value;
        });

        // Wishlist functionality
        wishlistBtn.addEventListener('click', function() {
            const isActive = this.classList.contains('active');

            if (isActive) {
                this.classList.remove('active');
                this.innerHTML = '<i class="fas fa-heart me-2"></i>أضف للمفضلة';
            } else {
                this.classList.add('active');
                this.innerHTML = '<i class="fas fa-heart me-2"></i>تمت الإضافة للمفضلة';
            }

            // Here you would make an AJAX call to add/remove from wishlist
            toggleWishlist(<?php echo $product_id; ?>, !isActive);
        });

        // Toggle wishlist function
        function toggleWishlist(productId, add) {
            fetch('ajax/toggle_wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    action: add ? 'add' : 'remove'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Wishlist updated successfully');
                } else {
                    console.error('Error updating wishlist:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // Format price function
        function formatPrice(price) {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: 'SAR'
            }).format(price);
        }

        // Enhanced form validation
        document.getElementById('addToCartForm').addEventListener('submit', function(e) {
            const requiredVariants = document.querySelectorAll('.variant-group');
            let allSelected = true;

            requiredVariants.forEach(group => {
                const selected = group.querySelector('.variant-option.selected');
                if (!selected) {
                    allSelected = false;
                }
            });

            if (!allSelected && requiredVariants.length > 0) {
                e.preventDefault();
                alert('يرجى اختيار جميع الخيارات المطلوبة');
                return false;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
            submitBtn.disabled = true;

            // Re-enable button after a delay (in case of errors)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    </script>
</body>
</html>
