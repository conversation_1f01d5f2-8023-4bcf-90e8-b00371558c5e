<?php
/**
 * Product Variant Class - كلاس متغيرات المنتج
 * Handles product variants like size, color, material, etc.
 */

class ProductVariant {
    private $conn;
    private $table_name = "product_variants";
    private $attributes_table = "product_attributes";
    private $attribute_values_table = "product_attribute_values";
    private $variant_attributes_table = "product_variant_attributes";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all product attributes
     * الحصول على جميع خصائص المنتجات
     */
    public function getAllAttributes() {
        $query = "SELECT * FROM {$this->attributes_table} ORDER BY display_name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get attribute values by attribute ID
     * الحصول على قيم الخاصية حسب معرف الخاصية
     */
    public function getAttributeValues($attribute_id) {
        $query = "SELECT * FROM {$this->attribute_values_table} 
                  WHERE attribute_id = :attribute_id 
                  ORDER BY display_value";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':attribute_id', $attribute_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create product variant
     * إنشاء متغير منتج
     */
    public function createVariant($product_id, $attributes, $price = null, $stock_quantity = 0, $sku = null) {
        try {
            $this->conn->beginTransaction();

            // Generate SKU if not provided
            if (empty($sku)) {
                $sku = $this->generateVariantSKU($product_id, $attributes);
            }

            // Insert variant
            $query = "INSERT INTO {$this->table_name} 
                      (product_id, sku, price, stock_quantity) 
                      VALUES (:product_id, :sku, :price, :stock_quantity)";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':product_id', $product_id);
            $stmt->bindParam(':sku', $sku);
            $stmt->bindParam(':price', $price);
            $stmt->bindParam(':stock_quantity', $stock_quantity);
            $stmt->execute();

            $variant_id = $this->conn->lastInsertId();

            // Insert variant attributes
            foreach ($attributes as $attribute_value_id) {
                $query = "INSERT INTO {$this->variant_attributes_table} 
                          (variant_id, attribute_value_id) 
                          VALUES (:variant_id, :attribute_value_id)";
                
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':variant_id', $variant_id);
                $stmt->bindParam(':attribute_value_id', $attribute_value_id);
                $stmt->execute();
            }

            $this->conn->commit();
            return $variant_id;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * Get product variants with attributes
     * الحصول على متغيرات المنتج مع الخصائص
     */
    public function getProductVariants($product_id) {
        $query = "SELECT 
                    pv.*,
                    GROUP_CONCAT(
                        CONCAT(pa.display_name, ':', pav.display_value, ':', COALESCE(pav.color_code, ''))
                        SEPARATOR '|'
                    ) as attributes_info
                  FROM {$this->table_name} pv
                  LEFT JOIN {$this->variant_attributes_table} pva ON pv.id = pva.variant_id
                  LEFT JOIN {$this->attribute_values_table} pav ON pva.attribute_value_id = pav.id
                  LEFT JOIN {$this->attributes_table} pa ON pav.attribute_id = pa.id
                  WHERE pv.product_id = :product_id
                  GROUP BY pv.id
                  ORDER BY pv.id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        
        $variants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Process attributes info
        foreach ($variants as &$variant) {
            $variant['attributes'] = [];
            if ($variant['attributes_info']) {
                $attrs = explode('|', $variant['attributes_info']);
                foreach ($attrs as $attr) {
                    $parts = explode(':', $attr);
                    if (count($parts) >= 2) {
                        $variant['attributes'][] = [
                            'name' => $parts[0],
                            'value' => $parts[1],
                            'color_code' => $parts[2] ?? null
                        ];
                    }
                }
            }
            unset($variant['attributes_info']);
        }
        
        return $variants;
    }

    /**
     * Get available attribute combinations for a product
     * الحصول على التركيبات المتاحة للخصائص لمنتج معين
     */
    public function getAvailableAttributes($product_id) {
        $query = "SELECT DISTINCT
                    pa.id as attribute_id,
                    pa.name as attribute_name,
                    pa.display_name,
                    pav.id as value_id,
                    pav.value,
                    pav.display_value,
                    pav.color_code
                  FROM {$this->table_name} pv
                  JOIN {$this->variant_attributes_table} pva ON pv.id = pva.variant_id
                  JOIN {$this->attribute_values_table} pav ON pva.attribute_value_id = pav.id
                  JOIN {$this->attributes_table} pa ON pav.attribute_id = pa.id
                  WHERE pv.product_id = :product_id AND pv.stock_quantity > 0
                  ORDER BY pa.display_name, pav.display_value";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Group by attribute
        $attributes = [];
        foreach ($results as $row) {
            $attr_id = $row['attribute_id'];
            if (!isset($attributes[$attr_id])) {
                $attributes[$attr_id] = [
                    'id' => $attr_id,
                    'name' => $row['attribute_name'],
                    'display_name' => $row['display_name'],
                    'values' => []
                ];
            }
            
            $attributes[$attr_id]['values'][] = [
                'id' => $row['value_id'],
                'value' => $row['value'],
                'display_value' => $row['display_value'],
                'color_code' => $row['color_code']
            ];
        }
        
        return array_values($attributes);
    }

    /**
     * Find variant by attributes
     * البحث عن متغير حسب الخصائص
     */
    public function findVariantByAttributes($product_id, $attribute_values) {
        if (empty($attribute_values)) {
            return null;
        }

        $placeholders = str_repeat('?,', count($attribute_values) - 1) . '?';
        
        $query = "SELECT pv.* 
                  FROM {$this->table_name} pv
                  JOIN {$this->variant_attributes_table} pva ON pv.id = pva.variant_id
                  WHERE pv.product_id = ? 
                  AND pva.attribute_value_id IN ($placeholders)
                  GROUP BY pv.id
                  HAVING COUNT(DISTINCT pva.attribute_value_id) = ?";
        
        $params = array_merge([$product_id], $attribute_values, [count($attribute_values)]);
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update variant
     * تحديث متغير
     */
    public function updateVariant($variant_id, $price = null, $stock_quantity = null, $sku = null) {
        $updates = [];
        $params = [];
        
        if ($price !== null) {
            $updates[] = "price = :price";
            $params[':price'] = $price;
        }
        
        if ($stock_quantity !== null) {
            $updates[] = "stock_quantity = :stock_quantity";
            $params[':stock_quantity'] = $stock_quantity;
        }
        
        if ($sku !== null) {
            $updates[] = "sku = :sku";
            $params[':sku'] = $sku;
        }
        
        if (empty($updates)) {
            return false;
        }
        
        $query = "UPDATE {$this->table_name} SET " . implode(', ', $updates) . " WHERE id = :variant_id";
        $params[':variant_id'] = $variant_id;
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    /**
     * Delete variant
     * حذف متغير
     */
    public function deleteVariant($variant_id) {
        $query = "DELETE FROM {$this->table_name} WHERE id = :variant_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':variant_id', $variant_id);
        return $stmt->execute();
    }

    /**
     * Generate variant SKU
     * توليد رمز متغير المنتج
     */
    private function generateVariantSKU($product_id, $attributes) {
        // Get product SKU
        $query = "SELECT sku FROM products WHERE id = :product_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $base_sku = $product['sku'] ?? 'PROD' . $product_id;
        
        // Add attribute codes
        $attr_codes = [];
        foreach ($attributes as $attr_value_id) {
            $query = "SELECT pav.value FROM {$this->attribute_values_table} pav WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $attr_value_id);
            $stmt->execute();
            $attr = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($attr) {
                $attr_codes[] = strtoupper(substr($attr['value'], 0, 2));
            }
        }
        
        $variant_sku = $base_sku . '-' . implode('', $attr_codes);
        
        // Ensure uniqueness
        $counter = 1;
        $original_sku = $variant_sku;
        while ($this->skuExists($variant_sku)) {
            $variant_sku = $original_sku . $counter;
            $counter++;
        }
        
        return $variant_sku;
    }

    /**
     * Check if SKU exists
     * التحقق من وجود رمز المنتج
     */
    private function skuExists($sku) {
        $query = "SELECT id FROM {$this->table_name} WHERE sku = :sku
                  UNION
                  SELECT id FROM products WHERE sku = :sku";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':sku', $sku);
        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    /**
     * Add new attribute
     * إضافة خاصية جديدة
     */
    public function addAttribute($name, $display_name) {
        $query = "INSERT INTO {$this->attributes_table} (name, display_name) VALUES (:name, :display_name)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':display_name', $display_name);
        $stmt->execute();
        return $this->conn->lastInsertId();
    }

    /**
     * Add attribute value
     * إضافة قيمة خاصية
     */
    public function addAttributeValue($attribute_id, $value, $display_value, $color_code = null) {
        $query = "INSERT INTO {$this->attribute_values_table} 
                  (attribute_id, value, display_value, color_code) 
                  VALUES (:attribute_id, :value, :display_value, :color_code)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':attribute_id', $attribute_id);
        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':display_value', $display_value);
        $stmt->bindParam(':color_code', $color_code);
        $stmt->execute();
        return $this->conn->lastInsertId();
    }
}
