<?php
/**
 * User Profile Page - صفحة الملف الشخصي
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';

// Require login
require_login();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize user class
$user_class = new User($db);

// Get user info
$user_info = $user_class->getUserById($_SESSION['user_id']);

$errors = array();
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        // Update profile
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $address = sanitize_input($_POST['address'] ?? '');
        $city = sanitize_input($_POST['city'] ?? '');
        $country = sanitize_input($_POST['country'] ?? 'Saudi Arabia');
        
        // Validation
        if (empty($first_name)) $errors[] = "الاسم الأول مطلوب";
        if (empty($last_name)) $errors[] = "اسم العائلة مطلوب";
        if (empty($phone)) $errors[] = "رقم الهاتف مطلوب";
        
        if (empty($errors)) {
            $profile_data = array(
                'first_name' => $first_name,
                'last_name' => $last_name,
                'phone' => $phone,
                'address' => $address,
                'city' => $city,
                'country' => $country
            );
            
            if ($user_class->updateProfile($_SESSION['user_id'], $profile_data)) {
                $success_message = "تم تحديث الملف الشخصي بنجاح!";
                $_SESSION['user_name'] = $first_name . ' ' . $last_name;
                $user_info = $user_class->getUserById($_SESSION['user_id']); // Refresh user info
            } else {
                $errors[] = "حدث خطأ أثناء تحديث الملف الشخصي";
            }
        }
    }
    
    if (isset($_POST['change_password'])) {
        // Change password
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_new_password = $_POST['confirm_new_password'] ?? '';
        
        // Validation
        if (empty($current_password)) $errors[] = "كلمة المرور الحالية مطلوبة";
        if (empty($new_password)) $errors[] = "كلمة المرور الجديدة مطلوبة";
        if (strlen($new_password) < 6) $errors[] = "كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل";
        if ($new_password !== $confirm_new_password) $errors[] = "كلمة المرور الجديدة وتأكيدها غير متطابقتان";
        
        if (empty($errors)) {
            if ($user_class->changePassword($_SESSION['user_id'], $current_password, $new_password)) {
                $success_message = "تم تغيير كلمة المرور بنجاح!";
            } else {
                $errors[] = "كلمة المرور الحالية غير صحيحة";
            }
        }
    }
}

$page_title = "الملف الشخصي - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .profile-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 2rem;
            text-align: center;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 3rem;
        }
        
        .sidebar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover {
            background: #667eea;
            color: white;
        }
        
        .sidebar .nav-link.active {
            background: #667eea;
            color: white;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-shopping-bag me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="dashboard.php">لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="orders.php">طلباتي</a></li>
                        <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-user-circle me-2"></i>
                        حسابي
                    </h5>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-bag me-2"></i>
                            طلباتي
                        </a>
                        <a class="nav-link active" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>
                            الملف الشخصي
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Profile Header -->
                <div class="profile-card card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3><?php echo $user_info['first_name'] . ' ' . $user_info['last_name']; ?></h3>
                        <p class="mb-0">@<?php echo $user_info['username']; ?></p>
                        <small>عضو منذ <?php echo date('Y/m/d', strtotime($user_info['created_at'])); ?></small>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>يرجى تصحيح الأخطاء التالية:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Profile Information Form -->
                <div class="form-section">
                    <h4 class="mb-4">
                        <i class="fas fa-user-edit me-2"></i>
                        المعلومات الشخصية
                    </h4>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($user_info['first_name']); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">اسم العائلة *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($user_info['last_name']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($user_info['phone']); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($user_info['city']); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($user_info['address']); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="country" class="form-label">البلد</label>
                            <select class="form-select" id="country" name="country">
                                <option value="Saudi Arabia" <?php echo $user_info['country'] === 'Saudi Arabia' ? 'selected' : ''; ?>>المملكة العربية السعودية</option>
                                <option value="UAE" <?php echo $user_info['country'] === 'UAE' ? 'selected' : ''; ?>>الإمارات العربية المتحدة</option>
                                <option value="Kuwait" <?php echo $user_info['country'] === 'Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                <option value="Qatar" <?php echo $user_info['country'] === 'Qatar' ? 'selected' : ''; ?>>قطر</option>
                                <option value="Bahrain" <?php echo $user_info['country'] === 'Bahrain' ? 'selected' : ''; ?>>البحرين</option>
                                <option value="Oman" <?php echo $user_info['country'] === 'Oman' ? 'selected' : ''; ?>>عمان</option>
                            </select>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Account Information -->
                <div class="form-section">
                    <h4 class="mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحساب
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($user_info['username']); ?>" readonly>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" value="<?php echo htmlspecialchars($user_info['email']); ?>" readonly>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لتغيير اسم المستخدم أو البريد الإلكتروني، يرجى التواصل مع خدمة العملاء.
                    </div>
                </div>

                <!-- Change Password -->
                <div class="form-section">
                    <h4 class="mb-4">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </h4>
                    
                    <form method="POST" id="passwordForm">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       required minlength="6">
                                <small class="text-muted">6 أحرف على الأقل</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_new_password" class="form-label">تأكيد كلمة المرور الجديدة *</label>
                                <input type="password" class="form-control" id="confirm_new_password" name="confirm_new_password" required>
                                <small id="passwordMatch" class="text-muted"></small>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Account Actions -->
                <div class="form-section">
                    <h4 class="mb-4">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات الحساب
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-2x text-primary mb-3"></i>
                                    <h6>تحميل البيانات</h6>
                                    <p class="text-muted small">احصل على نسخة من بياناتك</p>
                                    <button class="btn btn-outline-primary btn-sm">
                                        تحميل البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-times fa-2x text-danger mb-3"></i>
                                    <h6>حذف الحساب</h6>
                                    <p class="text-muted small">حذف الحساب نهائياً</p>
                                    <button class="btn btn-outline-danger btn-sm" onclick="confirmDeleteAccount()">
                                        حذف الحساب
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Password match checker
        document.getElementById('confirm_new_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            const matchText = document.getElementById('passwordMatch');
            
            if (confirmPassword === '') {
                matchText.textContent = '';
                matchText.className = 'text-muted';
            } else if (newPassword === confirmPassword) {
                matchText.textContent = 'كلمة المرور متطابقة ✓';
                matchText.className = 'text-success';
            } else {
                matchText.textContent = 'كلمة المرور غير متطابقة ✗';
                matchText.className = 'text-danger';
            }
        });
        
        // Password form validation
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_new_password').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقتان');
            }
        });
        
        // Phone number formatting
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.startsWith('966')) {
                    value = '+' + value;
                } else if (value.startsWith('05')) {
                    value = '+966' + value.substring(1);
                } else if (value.startsWith('5')) {
                    value = '+966' + value;
                }
            }
            e.target.value = value;
        });
        
        // Confirm delete account
        function confirmDeleteAccount() {
            if (confirm('هل أنت متأكد من حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع بياناتك وطلباتك نهائياً. هل تريد المتابعة؟')) {
                    // Implement account deletion logic
                    alert('تم إرسال طلب حذف الحساب. سيتم التواصل معك قريباً.');
                }
            }
        }
    </script>
</body>
</html>
