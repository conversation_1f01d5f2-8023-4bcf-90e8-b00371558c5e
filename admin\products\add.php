<?php
/**
 * Add Product Page - صفحة إضافة منتج
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../classes/Product.php';
require_once '../../classes/Category.php';
require_once '../../classes/ProductVariant.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$category_class = new Category($db);
$variant_class = new ProductVariant($db);

// Get categories and attributes
$categories = $category_class->getAllCategories();
$attributes = $variant_class->getAllAttributes();

$errors = array();
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $short_description = sanitize_input($_POST['short_description'] ?? '');
    $sku = sanitize_input($_POST['sku'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $sale_price = !empty($_POST['sale_price']) ? (float)$_POST['sale_price'] : null;
    $stock_quantity = (int)($_POST['stock_quantity'] ?? 0);
    $category_id = (int)($_POST['category_id'] ?? 0);
    $weight = !empty($_POST['weight']) ? (float)$_POST['weight'] : null;
    $dimensions = sanitize_input($_POST['dimensions'] ?? '');
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $meta_title = sanitize_input($_POST['meta_title'] ?? '');
    $meta_description = sanitize_input($_POST['meta_description'] ?? '');
    
    // Validation
    if (empty($name)) $errors[] = "اسم المنتج مطلوب";
    if (empty($description)) $errors[] = "وصف المنتج مطلوب";
    if (empty($short_description)) $errors[] = "الوصف المختصر مطلوب";
    if ($price <= 0) $errors[] = "السعر يجب أن يكون أكبر من صفر";
    if ($category_id <= 0) $errors[] = "يجب اختيار تصنيف";
    if ($sale_price && $sale_price >= $price) $errors[] = "سعر التخفيض يجب أن يكون أقل من السعر الأصلي";
    
    // Additional SKU validation
    if (!empty($sku) && $product_class->skuExists($sku)) {
        $errors[] = "رمز المنتج (SKU) موجود بالفعل";
    }

    if (empty($errors)) {
        $product_data = array(
            'name' => $name,
            'description' => $description,
            'short_description' => $short_description,
            'sku' => $sku,
            'price' => $price,
            'sale_price' => $sale_price,
            'stock_quantity' => $stock_quantity,
            'category_id' => $category_id,
            'weight' => $weight,
            'dimensions' => $dimensions,
            'is_featured' => $is_featured,
            'meta_title' => $meta_title ?: $name,
            'meta_description' => $meta_description ?: $short_description
        );

        try {
            $product_id = $product_class->createProduct($product_data);
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
            $product_id = false;
        }
        
        if ($product_id) {
            // Handle image uploads
            if (!empty($_FILES['images']['name'][0])) {
                $upload_dir = PRODUCT_IMAGES_PATH;
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                $is_first_image = true;
                foreach ($_FILES['images']['tmp_name'] as $key => $tmp_name) {
                    if (!empty($tmp_name)) {
                        $file_info = array(
                            'name' => $_FILES['images']['name'][$key],
                            'tmp_name' => $tmp_name,
                            'size' => $_FILES['images']['size'][$key],
                            'error' => $_FILES['images']['error'][$key]
                        );
                        
                        $uploaded_filename = upload_image($file_info, $upload_dir);
                        if ($uploaded_filename) {
                            $product_class->addProductImage(
                                $product_id, 
                                $uploaded_filename, 
                                $name, 
                                $is_first_image
                            );
                            $is_first_image = false;
                        }
                    }
                }
            }

            // Handle product variants
            if (isset($_POST['variants']) && !empty($_POST['variants'])) {
                foreach ($_POST['variants'] as $variant_data) {
                    if (!empty($variant_data['attributes'])) {
                        $variant_class->createVariant(
                            $product_id,
                            $variant_data['attributes'],
                            $variant_data['price'] ?: null,
                            $variant_data['stock'] ?: 0,
                            $variant_data['sku'] ?: null
                        );
                    }
                }
            }

            $success = true;
            header('Location: index.php?added=success');
            exit();
        } else {
            $errors[] = "حدث خطأ أثناء إضافة المنتج";
        }
    }
}

$page_title = "إضافة منتج جديد - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .image-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            margin: 0.5rem;
        }
        
        .image-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .image-upload-area:hover {
            border-color: #0d6efd;
            background: #f8f9ff;
        }
        
        .image-upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
        }
        
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .preview-item {
            position: relative;
            display: inline-block;
        }
        
        .preview-remove {
            position: absolute;
            top: -8px;
            left: -8px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="index.php" class="btn btn-outline-secondary ms-3">
                        <i class="fas fa-arrow-right ms-1"></i> </a>
                    <h4 class="mb-0">إضافة منتج جديد</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt ms-2 ms-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle ms-2 ms-1"></i> <strong>يرجى تصحيح الأخطاء التالية:</strong>
                <ul class="mb-0 mt-2">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" id="productForm">
            <div class="row">
                <!-- Main Information -->
                <div class="col-lg-8">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle ms-2 ms-1"></i>المعلومات الأساسية
                        </h5>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم المنتج *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sku" class="form-label">
                                    رقم المنتج (SKU)
                                    <small class="text-muted">(اختياري - سيتم توليده تلقائياً إذا ترك فارغاً)</small>
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="sku" name="sku"
                                           value="<?php echo htmlspecialchars($_POST['sku'] ?? ''); ?>"
                                           placeholder="مثال: PROD-001">
                                    <button type="button" class="btn btn-outline-secondary" id="generateSku" title="توليد رقم تلقائي">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                </div>
                                <div class="form-text">سيتم توليد رقم فريد تلقائياً إذا ترك هذا الحقل فارغاً</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="category_id" class="form-label">التصنيف *</label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">اختر التصنيف</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo $category['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="short_description" class="form-label">الوصف المختصر *</label>
                            <textarea class="form-control" id="short_description" name="short_description" 
                                      rows="3" required maxlength="500"><?php echo htmlspecialchars($_POST['short_description'] ?? ''); ?></textarea>
                            <small class="text-muted">الحد الأقصى 500 حرف</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف التفصيلي *</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="6" required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Pricing & Inventory -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-money-bill-wave ms-2 ms-1"></i>السعر والمخزون
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">السعر الأصلي (ريال) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       value="<?php echo $_POST['price'] ?? ''; ?>" 
                                       step="0.01" min="0" required>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="sale_price" class="form-label">سعر التخفيض (ريال)</label>
                                <input type="number" class="form-control" id="sale_price" name="sale_price" 
                                       value="<?php echo $_POST['sale_price'] ?? ''; ?>" 
                                       step="0.01" min="0">
                                <small class="text-muted">اتركه فارغاً إذا لم يكن هناك تخفيض</small>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="stock_quantity" class="form-label">كمية المخزون *</label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       value="<?php echo $_POST['stock_quantity'] ?? '0'; ?>" 
                                       min="0" required>
                            </div>
                        </div>
                    </div>

                    <!-- Product Images -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-images ms-2 ms-1"></i>صور المنتج
                        </h5>
                        
                        <div class="image-upload-area" onclick="document.getElementById('images').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3 ms-1"></i> <h6>اضغط لرفع الصور أو اسحبها هنا</h6>
                            <p class="text-muted mb-0">يمكنك رفع عدة صور (JPG, PNG, GIF, WEBP)</p>
                            <input type="file" id="images" name="images[]" multiple accept="image/*" class="d-none">
                        </div>
                        
                        <div class="preview-container" id="imagePreview"></div>
                    </div>

                    <!-- Additional Information -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-cog ms-2 ms-1"></i>معلومات إضافية
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="weight" class="form-label">الوزن (كجم)</label>
                                <input type="number" class="form-control" id="weight" name="weight" 
                                       value="<?php echo $_POST['weight'] ?? ''; ?>" 
                                       step="0.01" min="0">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="dimensions" class="form-label">الأبعاد</label>
                                <input type="text" class="form-control" id="dimensions" name="dimensions" 
                                       value="<?php echo htmlspecialchars($_POST['dimensions'] ?? ''); ?>" 
                                       placeholder="مثال: 30×20×10 سم">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">عنوان SEO</label>
                            <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                   value="<?php echo htmlspecialchars($_POST['meta_title'] ?? ''); ?>" 
                                   maxlength="200">
                            <small class="text-muted">سيتم استخدام اسم المنتج إذا ترك فارغاً</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="meta_description" class="form-label">وصف SEO</label>
                            <textarea class="form-control" id="meta_description" name="meta_description" 
                                      rows="2" maxlength="300"><?php echo htmlspecialchars($_POST['meta_description'] ?? ''); ?></textarea>
                            <small class="text-muted">سيتم استخدام الوصف المختصر إذا ترك فارغاً</small>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Publish Options -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-eye ms-2 ms-1"></i>خيارات النشر
                        </h5>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" 
                                   <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_featured">
                                منتج مميز
                            </label>
                            <small class="text-muted d-block">سيظهر في قسم المنتجات المميزة</small>
                        </div>
                    </div>

                    <!-- Product Variants -->
                    <div class="form-section">
                        <h5 class="mb-4">
                            <i class="fas fa-layer-group ms-2 ms-1"></i>متغيرات المنتج
                        </h5>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="has_variants" name="has_variants">
                                <label class="form-check-label" for="has_variants">
                                    هذا المنتج له متغيرات (مقاسات، ألوان، إلخ)
                                </label>
                            </div>
                        </div>

                        <div id="variants-section" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">الخصائص المتاحة:</label>
                                <div class="row">
                                    <?php foreach ($attributes as $attribute): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input attribute-checkbox"
                                                   id="attr_<?php echo $attribute['id']; ?>"
                                                   value="<?php echo $attribute['id']; ?>"
                                                   data-name="<?php echo $attribute['name']; ?>"
                                                   data-display="<?php echo $attribute['display_name']; ?>">
                                            <label class="form-check-label" for="attr_<?php echo $attribute['id']; ?>">
                                                <?php echo $attribute['display_name']; ?>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div id="variant-builder" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    اختر قيم الخصائص لإنشاء المتغيرات تلقائياً
                                </div>

                                <div id="attribute-values-container"></div>

                                <div class="mb-3">
                                    <button type="button" class="btn btn-primary" id="generate-variants">
                                        <i class="fas fa-magic me-2"></i>إنشاء المتغيرات
                                    </button>
                                </div>

                                <div id="variants-container"></div>
                            </div>
                        </div>
                    </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save ms-2 ms-1"></i>حفظ المنتج
                            </button>
                            
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times ms-2 ms-1"></i>إلغاء
                            </a>
                        </div>
                    </div>

                    <!-- Tips -->
                    <div class="form-section">
                        <h6 class="mb-3">
                            <i class="fas fa-lightbulb ms-2 ms-1"></i>نصائح
                        </h6>
                        
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>استخدم صوراً عالية الجودة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>اكتب وصفاً مفصلاً وواضحاً
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>حدد السعر بدقة
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success ms-2 ms-1"></i>اختر التصنيف المناسب
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Image upload handling
        const imageInput = document.getElementById('images');
        const imagePreview = document.getElementById('imagePreview');
        const uploadArea = document.querySelector('.image-upload-area');
        
        imageInput.addEventListener('change', handleImageSelect);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            imageInput.files = files;
            handleImageSelect();
        });
        
        function handleImageSelect() {
            const files = imageInput.files;
            imagePreview.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" class="image-preview" alt="Preview">
                            <button type="button" class="preview-remove" onclick="removeImage(this, ${i})">
                                <i class="fas fa-times ms-1"></i> </button>
                        `;
                        imagePreview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                }
            }
        }
        
        function removeImage(button, index) {
            const dt = new DataTransfer();
            const files = imageInput.files;

            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }

            imageInput.files = dt.files;
            button.parentElement.remove();

            // Re-render preview to update indices
            handleImageSelect();
        }
        
        // Enhanced SKU generation
        function generateSKU() {
            const name = document.getElementById('name').value;
            if (!name) {
                alert('يرجى إدخال اسم المنتج أولاً');
                return;
            }

            // Create base SKU from product name
            let baseSku = name.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().substring(0, 6);
            if (!baseSku) {
                baseSku = 'PROD';
            }

            // Add date and random number
            const date = new Date();
            const dateStr = date.getFullYear().toString().substr(-2) +
                           String(date.getMonth() + 1).padStart(2, '0') +
                           String(date.getDate()).padStart(2, '0');
            const randomNum = Math.floor(Math.random() * 900) + 100;

            const sku = baseSku + dateStr + randomNum;
            document.getElementById('sku').value = sku;
        }

        // Auto-generate SKU when name changes (only if SKU is empty)
        document.getElementById('name').addEventListener('input', function() {
            const skuField = document.getElementById('sku');
            if (!skuField.value) {
                generateSKU();
            }
        });

        // Manual SKU generation button
        document.getElementById('generateSku').addEventListener('click', generateSKU);
        
        // Auto-generate meta title
        document.getElementById('name').addEventListener('input', function() {
            if (!document.getElementById('meta_title').value) {
                document.getElementById('meta_title').value = this.value;
            }
        });
        
        // Auto-generate meta description
        document.getElementById('short_description').addEventListener('input', function() {
            if (!document.getElementById('meta_description').value) {
                document.getElementById('meta_description').value = this.value;
            }
        });
        
        // Form validation
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const price = parseFloat(document.getElementById('price').value);
            const salePrice = parseFloat(document.getElementById('sale_price').value);
            
            if (salePrice && salePrice >= price) {
                e.preventDefault();
                alert('سعر التخفيض يجب أن يكون أقل من السعر الأصلي');
                return;
            }
            
            // Check if at least one image is selected
            const images = document.getElementById('images').files;
            if (images.length === 0) {
                if (!confirm('لم تقم برفع أي صور للمنتج. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return;
                }
            }
        });

        // Product Variants JavaScript
        const hasVariantsCheckbox = document.getElementById('has_variants');
        const variantsSection = document.getElementById('variants-section');
        const attributeCheckboxes = document.querySelectorAll('.attribute-checkbox');
        const variantBuilder = document.getElementById('variant-builder');
        const attributeValuesContainer = document.getElementById('attribute-values-container');
        const generateVariantsBtn = document.getElementById('generate-variants');
        const variantsContainer = document.getElementById('variants-container');

        // Toggle variants section
        hasVariantsCheckbox.addEventListener('change', function() {
            variantsSection.style.display = this.checked ? 'block' : 'none';
            if (!this.checked) {
                variantBuilder.style.display = 'none';
                attributeCheckboxes.forEach(cb => cb.checked = false);
            }
        });

        // Handle attribute selection
        attributeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateVariantBuilder);
        });

        function updateVariantBuilder() {
            const selectedAttributes = Array.from(attributeCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => ({
                    id: cb.value,
                    name: cb.dataset.name,
                    display: cb.dataset.display
                }));

            if (selectedAttributes.length > 0) {
                variantBuilder.style.display = 'block';
                loadAttributeValues(selectedAttributes);
            } else {
                variantBuilder.style.display = 'none';
            }
        }

        async function loadAttributeValues(attributes) {
            attributeValuesContainer.innerHTML = '';

            for (const attr of attributes) {
                try {
                    const response = await fetch(`get_attribute_values.php?id=${attr.id}`);
                    const values = await response.json();

                    const container = document.createElement('div');
                    container.className = 'mb-3';
                    container.innerHTML = `
                        <label class="form-label">${attr.display}:</label>
                        <div class="row" data-attribute-id="${attr.id}">
                            ${values.map(value => `
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input attribute-value-checkbox"
                                               id="val_${value.id}" value="${value.id}"
                                               data-attribute-id="${attr.id}"
                                               data-value="${value.value}"
                                               data-display="${value.display_value}"
                                               data-color="${value.color_code || ''}">
                                        <label class="form-check-label d-flex align-items-center" for="val_${value.id}">
                                            ${value.color_code ? `<span class="color-swatch me-2" style="background-color: ${value.color_code}"></span>` : ''}
                                            ${value.display_value}
                                        </label>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                    attributeValuesContainer.appendChild(container);
                } catch (error) {
                    console.error('Error loading attribute values:', error);
                }
            }
        }

        // Generate variants
        generateVariantsBtn.addEventListener('click', function() {
            const selectedValues = {};

            document.querySelectorAll('.attribute-value-checkbox:checked').forEach(cb => {
                const attrId = cb.dataset.attributeId;
                if (!selectedValues[attrId]) {
                    selectedValues[attrId] = [];
                }
                selectedValues[attrId].push({
                    id: cb.value,
                    value: cb.dataset.value,
                    display: cb.dataset.display,
                    color: cb.dataset.color
                });
            });

            generateVariantCombinations(selectedValues);
        });

        function generateVariantCombinations(selectedValues) {
            const attributes = Object.keys(selectedValues);
            if (attributes.length === 0) return;

            const combinations = cartesianProduct(Object.values(selectedValues));

            variantsContainer.innerHTML = '<h6 class="mb-3">المتغيرات المُنشأة:</h6>';

            combinations.forEach((combination, index) => {
                const variantDiv = document.createElement('div');
                variantDiv.className = 'variant-item border rounded p-3 mb-3';

                const attributeNames = combination.map(attr => attr.display).join(' - ');
                const attributeIds = combination.map(attr => attr.id);

                variantDiv.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <strong>${attributeNames}</strong>
                            <input type="hidden" name="variants[${index}][attributes][]" value="${attributeIds.join(',')}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">السعر:</label>
                            <input type="number" step="0.01" class="form-control form-control-sm"
                                   name="variants[${index}][price]" placeholder="اختياري">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المخزون:</label>
                            <input type="number" class="form-control form-control-sm"
                                   name="variants[${index}][stock]" value="0">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">SKU:</label>
                            <input type="text" class="form-control form-control-sm"
                                   name="variants[${index}][sku]" placeholder="اختياري">
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.parentElement.parentElement.remove()">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                variantsContainer.appendChild(variantDiv);
            });
        }

        function cartesianProduct(arrays) {
            return arrays.reduce((acc, curr) => {
                const result = [];
                acc.forEach(a => {
                    curr.forEach(c => {
                        result.push([...a, c]);
                    });
                });
                return result;
            }, [[]]);
        }
    </script>

    <style>
        .color-swatch {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #ddd;
            display: inline-block;
        }

        .variant-item {
            background: #f8f9fa;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</body>
</html>
